const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const path = require('path');
const ini = require('ini');
const fs = require('fs');

// Get config path from command line arguments or use default
const configPath = process.argv.find(arg => arg.startsWith('--config='))
  ? process.argv.find(arg => arg.startsWith('--config=')).split('=')[1]
  : './conf/config.ini';

console.log(`Database loading configuration from: ${configPath}`);
const config = ini.parse(fs.readFileSync(path.resolve(configPath), 'utf-8'));
console.log('Database config sections:', Object.keys(config));
console.log('Admin section exists:', !!config.admin);
if (config.admin) {
  console.log('Admin config:', config.admin);
}

// Docker-aware host resolution
function resolveDbHost(configHost) {
  // More robust Docker detection
  const dockerIndicators = [
    fs.existsSync('/.dockerenv'),                    // Standard Docker indicator
    process.env.DOCKER_CONTAINER === 'true',        // Our custom indicator  
    process.env.NODE_ENV === 'production',          // Production mode
    process.env.HOSTNAME && process.env.HOSTNAME.length === 12, // Docker hostname pattern
    process.env.container === 'docker'              // Another indicator
  ];
  
  const isInDocker = dockerIndicators.some(indicator => indicator);
  
  console.log('Docker detection:', {
    dockerenv: fs.existsSync('/.dockerenv'),
    dockerContainer: process.env.DOCKER_CONTAINER,
    nodeEnv: process.env.NODE_ENV,
    hostname: process.env.HOSTNAME,
    container: process.env.container,
    isInDocker: isInDocker,
    originalHost: configHost
  });
  
  // If in Docker and host is localhost, use host.docker.internal to reach host machine
  if (isInDocker && (configHost === 'localhost' || configHost === '127.0.0.1')) {
    console.log(`Docker environment detected: translating ${configHost} to host.docker.internal`);
    return 'host.docker.internal';
  }
  
  console.log(`Using original host: ${configHost}`);
  return configHost;
}

// PostgreSQL connection configuration
const pgConfig = {
  host: resolveDbHost(config.database['database-host']),
  port: config.database['database-port'],
  user: config.database['database-user'],
  password: config.database['database-password'],
  database: config.database['database-name'],
  max: config.database.max_connections || 20,
  ssl: config.database.ssl === 'true'
};

console.log(`Connecting to PostgreSQL at ${pgConfig.host}:${pgConfig.port}`);

const pool = new Pool(pgConfig);

// Test the connection
pool.connect((err, client, release) => {
  if (err) {
    console.error('Error connecting to PostgreSQL:', err);
    process.exit(1);
  }
  console.log(`Connected to PostgreSQL database: ${pgConfig.database}`);
  release();
});

// Helper function to convert SQLite '?' placeholders to PostgreSQL '$1' style placeholders
function convertPlaceholders(text) {
  let paramIndex = 0;
  return text.replace(/\?/g, () => `$${++paramIndex}`);
}

// Create a database interface object that mimics the SQLite interface
// to minimize changes in existing code
const db = {
  // Query executor function
  async query(text, params) {
    try {
      const convertedText = convertPlaceholders(text);
      const result = await pool.query(convertedText, params);
      return result;
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  },

  // For code compatibility with SQLite, we'll provide similar methods
  // but adapted for PostgreSQL
  prepare(text) {
    const convertedText = convertPlaceholders(text);

    return {
      get: async (...params) => {
        try {
          const result = await pool.query(convertedText, params);
          return result.rows[0] || null;
        } catch (error) {
          console.error(`Error in db.prepare().get() with query '${convertedText}':`, error);
          throw error;
        }
      },
      all: async (...params) => {
        try {
          const result = await pool.query(convertedText, params);
          return result.rows;
        } catch (error) {
          console.error(`Error in db.prepare().all() with query '${convertedText}':`, error);
          throw error;
        }
      },
      run: async (...params) => {
        try {
          const result = await pool.query(convertedText, params);
          // For compatibility with better-sqlite3, we add lastInsertRowid
          // This approximation works only for single inserts
          let lastInsertRowid = null;
          if (result.rows && result.rows[0] && result.rows[0].id) {
            lastInsertRowid = result.rows[0].id;
          }

          return {
            changes: result.rowCount || 0,
            lastInsertRowid: lastInsertRowid
          };
        } catch (error) {
          console.error(`Error in db.prepare().run() with query '${convertedText}':`, error);
          throw error;
        }
      }
    };
  },

  // Execute raw SQL
  async exec(text) {
    try {
      await pool.query(text);
    } catch (error) {
      console.error('SQL execution error:', error);
      throw error;
    }
  }
};

async function initializeDatabase() {
  console.log('Checking PostgreSQL database connection');

  try {
    // Run migrations
    await runMigrations();

    // Run document table creation script
    try {
      // Check if the script exists before attempting to run it
      const fixScriptPath = path.resolve(__dirname, 'scripts/sql/fix_documents_table.sql');
      if (fs.existsSync(fixScriptPath)) {
        await runMigration('scripts/sql/fix_documents_table.sql');
        console.log('Documents table creation/fix completed');
      } else {
        console.log('Fix documents table script not found, skipping');
      }
    } catch (error) {
      console.error('Error creating/fixing documents table:', error.message);
    }

    // Add name column to users table if it doesn't exist
    try {
      console.log('Checking if name column exists in users table...');
      await pool.query(`
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'users'
            AND column_name = 'name'
          ) THEN
            ALTER TABLE users ADD COLUMN name VARCHAR(100);
            RAISE NOTICE 'Added name column to users table';
          ELSE
            RAISE NOTICE 'name column already exists in users table';
          END IF;
        END $$;
      `);
      console.log('Name column check completed');
    } catch (error) {
      console.error('Error checking/adding name column:', error.message);
    }

    // Check if admin user exists, if not create default admin
    const defaultUsername = config.admin?.default_username || 'admin';
    const userQuery = await pool.query("SELECT * FROM users WHERE username = $1", [defaultUsername]);

    if (userQuery.rows.length === 0) {
      const defaultPassword = config.admin?.default_password || 'admin';
      const defaultEmail = config.admin?.default_email || 'admin@localhost';
      const hashedPassword = bcrypt.hashSync(defaultPassword, 10);

      // Modified query to include name field
      await pool.query(
        "INSERT INTO users (username, password, email, role, name) VALUES ($1, $2, $3, $4, $5) RETURNING id",
        [defaultUsername, hashedPassword, defaultEmail, 'admin', 'Administrator']
      );
      console.log('Default admin user created');
    } else {
      // Update admin user to add name if it's missing
      if (userQuery.rows[0].name === null || userQuery.rows[0].name === undefined) {
        await pool.query(
          "UPDATE users SET name = $1 WHERE username = $2",
          ['Administrator', defaultUsername]
        );
        console.log('Updated admin user with name');
      }
      console.log('Admin user already exists');
    }
  } catch (error) {
    console.error('Database initialization error:', error);
    // Don't exit the process, just log the error
    console.error('More details:', error.message);
  }
}

process.on('exit', () => {
  // Close the pool when the application exits
  pool.end();
  console.log('Database connection pool closed');
});

/**
 * Runs a migration script file
 */
const runMigration = async (scriptPath) => {
  try {
    // Attempt to find the script in multiple locations
    let resolvedPath;
    const possiblePaths = [
      path.resolve(__dirname, '..', scriptPath), // Try relative to project root
      path.resolve(__dirname, scriptPath),       // Try relative to src
      path.resolve(scriptPath)                   // Try absolute path
    ];
    
    for (const testPath of possiblePaths) {
      if (fs.existsSync(testPath)) {
        resolvedPath = testPath;
        break;
      }
    }
    
    if (!resolvedPath) {
      console.log(`Migration script not found: ${scriptPath}`);
      return false;
    }
    
    const scriptContent = fs.readFileSync(resolvedPath, 'utf8');
    await db.query(scriptContent);
    console.log(`Migration script ${scriptPath} executed successfully`);
    return true;
  } catch (error) {
    console.error(`Error executing migration script ${scriptPath}:`, error);
    return false;
  }
};

/**
 * Runs database migrations for model_id column
 */
const runModelIdMigration = async () => {
  try {
    await runMigration('scripts/sql/add_model_id_to_ai_models.sql');
    console.log('Model ID migration completed');
    return true;
  } catch (error) {
    console.error('Error running model_id migration:', error);
    return false;
  }
};

/**
 * Runs all migrations in order
 */
const runMigrations = async () => {
  try {
    console.log('Running database migrations...');

    // Get all migration files
    const migrationsDir = path.join(__dirname, 'migrations');
    if (!fs.existsSync(migrationsDir)) {
      console.log('No migrations directory found, skipping migrations');
      return;
    }

    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js'))
      .sort(); // Sort to ensure migrations run in order

    // Get list of applied migrations
    let appliedMigrationNames = [];
    try {
      // Create migrations table if it doesn't exist
      await pool.query(`
        CREATE TABLE IF NOT EXISTS schema_migrations (
          id SERIAL PRIMARY KEY,
          version VARCHAR(255) NOT NULL UNIQUE,
          applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          description TEXT,
          name VARCHAR(255)
        )
      `);
    } catch (error) {
      console.error('Error creating migrations table:', error);
      throw error;
    }

    // Get list of applied migrations
    try {
      // Try to get migrations by name first (for compatibility)
      let appliedMigrations = await pool.query('SELECT name FROM schema_migrations WHERE name IS NOT NULL');
      
      if (appliedMigrations.rows.length === 0) {
        // If no migrations found by name, try with version
        appliedMigrations = await pool.query('SELECT version FROM schema_migrations WHERE version IS NOT NULL');
        appliedMigrationNames = appliedMigrations.rows.map(row => row.version);
      } else {
        appliedMigrationNames = appliedMigrations.rows.map(row => row.name);
      }
      
      console.log('Applied migrations:', appliedMigrationNames);
    } catch (error) {
      console.error('Error getting applied migrations, assuming none applied:', error.message);
      // Continue with empty list - will try to apply all migrations
    }

    // Run each migration that hasn't been applied yet
    for (const file of migrationFiles) {
      if (!appliedMigrationNames.includes(file)) {
        console.log(`Running migration: ${file}`);
        const migration = require(path.join(migrationsDir, file));

        try {
          await migration.up();

          // Record that the migration has been applied
          // Extract version number from file name (e.g., 005_add_documents_table.js -> 005)
          const versionMatch = file.match(/^(\d+)_/);
          const version = versionMatch ? versionMatch[1] : file;
          const description = file.replace(/\.js$/, '').replace(/^\d+_/, '').replace(/_/g, ' ');
          
          // Insert with both name and version for compatibility
          try {
            await pool.query(
              'INSERT INTO schema_migrations (name, version, description) VALUES ($1, $2, $3) ON CONFLICT (version) DO NOTHING',
              [file, version, description]
            );
            console.log(`Migration ${file} recorded successfully`);
          } catch (insertError) {
            // If there's still an error after using ON CONFLICT, try with just the name
            console.warn(`Warning: Could not record migration with version ${version}, trying with name only`);
            try {
              await pool.query(
                'INSERT INTO schema_migrations (name, description) VALUES ($1, $2)',
                [file, description]
              );
              console.log(`Migration ${file} recorded with name only`);
            } catch (nameError) {
              console.error(`Warning: Failed to record migration ${file}, but continuing:`, nameError.message);
              // Continue execution - the migration ran successfully even if we couldn't record it
            }
          }
          
          console.log(`Migration ${file} completed successfully`);
        } catch (error) {
          console.error(`Error running migration ${file}:`, error);
          throw error;
        }
      } else {
        console.log(`Migration ${file} already applied, skipping`);
      }
    }

    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Error running migrations:', error);
    throw error;
  }
};

// Export functions
module.exports = {
  db,
  pool, // Export the pool for direct access if needed
  initializeDatabase,
  runMigration,
  runModelIdMigration,
  runMigrations
};