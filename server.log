nohup: ignoring input

> platform-dashboard@1.0.0 dev
> nodemon src/server.js --config=./conf/config.ini

[33m[nodemon] 3.1.10[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): src/**/* client/src/**/*[39m
[33m[nodemon] watching extensions: js,json,jsx,ts,tsx[39m
[32m[nodemon] starting `node src/server.js --config=./conf/config.ini`[39m
node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: EBADF: bad file descriptor, read
Emitted 'error' event on ReadStream instance at:
    at emitErrorNT (node:internal/streams/destroy:169:8)
    at errorOrDestroy (node:internal/streams/destroy:238:7)
    at node:internal/fs/streams:272:9
    at FSReqCallback.wrapper [as oncomplete] (node:fs:687:5) {
  errno: -9,
  code: 'EBADF',
  syscall: 'read'
}

Node.js v20.12.2
