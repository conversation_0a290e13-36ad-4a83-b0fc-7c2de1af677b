[paths]
data = ./data
logs = ./logs
plugins = ./plugins
static_files = ./client/build
documents = ./DATA/documents
embeddings = ./DATA/embeddings
chroma_data = ./DATA/chroma_data
vector_store = ./DATA/vector_store

[python]
interpreter = python
# Use Python 3.9 from virtual environment for better PDF text extraction
# Created using the installvenv.sh script in the python directory

[server]
protocol = http
domain = 0.0.0.0
port = 5641
static_root_path = ./client/build
serve_from_sub_path = false

[database]
# SQLite configuration (commented out but preserved for reference)
# type = sqlite
# path = ./data/app.db
# max_connections = 10
# log_queries = false

# PostgreSQL configuration
database-type = postgres
database-host = ************
database-port = 5432
database-user = postgres
database-password = root
database-name = copilot
max_connections = 100
log_queries = false
ssl = false

[docker]
# Docker services configuration
# ChromaDB Docker container details
chromadb_protocol = http
chromadb_host = chromadb
chromadb_port = 8000
# Redis Docker container details
redis_host = redis
redis_port = 6379
# PostgreSQL Docker container details (if using containerized DB)
postgres_host = ************
postgres_port = 5432

[redis]
host = redis
port = 6379
password =
max_retries = 3
retry_delay = 100
connection_timeout = 5000

[embedding_service]
# Embedding Service Configuration
enabled = true
protocol = http
host = embedding-service
port = 3579
# Full URL will be constructed as: {protocol}://{host}:{port}
connection_timeout = 120000
request_timeout = 180000
# Ollama configuration for embedding service
ollama_host = ************
ollama_port = 11434
# Redis caching configuration
cache_enabled = true
cache_ttl_seconds = 3600
# Rate limiting configuration
rate_limit_requests = 1000
rate_limit_window_minutes = 15
# Batch processing configuration
batch_size = 50
max_batch_size = 1000
# Docker container name
docker_container = productdemo-embedding-service

[document_queue]
# Document processing queue configuration
worker_count = 3
concurrency = 3
max_jobs_per_worker = 10
retry_attempts = 3
remove_completed_after = 50
remove_failed_after = 20
stalled_interval = 30000
max_stalled_count = 1
job_timeout = 600000

[image_processing]
# Image processing configuration for RAG system
enabled = true
docker_container = productdemo-image-processor
min_size_kb = 5
min_width = 100
min_height = 100
max_images_per_document = 100
ocr_enabled = true
base64_encoding = true
# Image search configuration for RAG
max_images_in_response = 3
similarity_threshold = 0.7
keyword_boost_factor = 1.2

[document_processing]
# Automatic file cleanup configuration
auto_cleanup_files = true
cleanup_delay_seconds = 30
keep_failed_files = true
cleanup_log_level = info
# Maximum file age before cleanup (for manual cleanup scripts)
max_file_age_days = 7
# Enable cleanup statistics tracking
track_cleanup_stats = true

[chat2sql]
# Chat2SQL service configuration
enabled = true
protocol = http
host = localhost
port = 5000
# Full URL will be constructed as: {protocol}://{host}:{port}
connection_timeout = 30000
request_timeout = 60000
# Docker container name
docker_container = productdemo-chat2sql

[security]
secret_key = your_session_secret_here
cookie_secure = false
cookie_samesite = lax
cookie_max_age = 86400000
allow_embedding = true
strict_transport_security = false

[auth]
login_maximum_inactive_lifetime_days = 7
login_maximum_lifetime_days = 30
disable_login_form = false
disable_signout_menu = false

[users]
allow_sign_up = true
allow_org_create = false
auto_assign_org = true
auto_assign_org_role = Viewer

[admin]
default_username = admin
default_password = admin
default_email = admin@localhost

[frontend]
app_mode = development
app_name = Product Demo
app_title = Product Demo
app_sub_url =
custom_css_path =
serve_static = true
api_url = /api
default_theme = light

[mcp_orchestrator]
# MCP Orchestrator Service Configuration
use_service = true
protocol = http
host = mcp-orchestrator
port = 3581
url = http://mcp-orchestrator:3581
fallback_enabled = true

[mcp-server]
mcp_terminal_info_endpoint = /info
#endpoint to get the terminal info

mcp_terminal_name_1 = mcp-terminal_executor
mcp_terminal_command_1 = curl -fsSL https://raw.githubusercontent.com/yaswanth-deriv/mcp-terminal-executor/main/install.sh | bash
mcp_terminal_command_1_install_cmd = mcp-terminal_executor start
mcp_terminal_command_1_stop_cmd = mcp-terminal_executor stop
mcp_terminal_command_1_restart_cmd = mcp-terminal_executor restart
mcp_terminal_command_1_status_cmd = mcp-terminal_executor status
mcp_terminal_command_1_uninstall_cmd = mcp-terminal_executor uninstall
mcp_terminal_command_default_host_1 = localhost
mcp_terminal_command_default_timeout_1 = 300
mcp_terminal_command_default_timeout_unit_1 = seconds
mcp_terminal_name_1_information_endpoint = /info
mcp_terminal_name_1_fetch_tool_endpoint = /tools
mcp_terminal_name_1_sse_endpoint = /sse
mcp_terminal_name_1_messages_endpoint = /messages

[analytics]
reporting_enabled = false
check_for_updates = true

[log]
mode = console
level = info
filters =