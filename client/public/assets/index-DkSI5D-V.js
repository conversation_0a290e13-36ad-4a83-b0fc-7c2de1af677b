import{r as R,a as dt,u as be,N as G,b as Le,O as ft,L as pt,B as mt,R as ht,c as B,d as xt}from"./vendor-Bi6iyY0m.js";import{F as bt,a as yt,b as gt,c as wt,d as jt,e as Nt,f as Et,g as Rt,h as St,i as Ot,j as At}from"./icons-D6L0TbxO.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function r(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(s){if(s.ep)return;s.ep=!0;const o=r(s);fetch(s.href,o)}})();var De={exports:{}},Z={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tt=R,vt=Symbol.for("react.element"),Ct=Symbol.for("react.fragment"),kt=Object.prototype.hasOwnProperty,Ft=Tt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Pt={key:!0,ref:!0,__self:!0,__source:!0};function Be(e,t,r){var n,s={},o=null,a=null;r!==void 0&&(o=""+r),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(a=t.ref);for(n in t)kt.call(t,n)&&!Pt.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)s[n]===void 0&&(s[n]=t[n]);return{$$typeof:vt,type:e,key:o,ref:a,props:s,_owner:Ft.current}}Z.Fragment=Ct;Z.jsx=Be;Z.jsxs=Be;De.exports=Z;var i=De.exports,ue={},Ne=dt;ue.createRoot=Ne.createRoot,ue.hydrateRoot=Ne.hydrateRoot;function qe(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ut}=Object.prototype,{getPrototypeOf:ye}=Object,ee=(e=>t=>{const r=Ut.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),k=e=>(e=e.toLowerCase(),t=>ee(t)===e),te=e=>t=>typeof t===e,{isArray:q}=Array,I=te("undefined");function _t(e){return e!==null&&!I(e)&&e.constructor!==null&&!I(e.constructor)&&v(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const $e=k("ArrayBuffer");function Lt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&$e(e.buffer),t}const Dt=te("string"),v=te("function"),Me=te("number"),re=e=>e!==null&&typeof e=="object",Bt=e=>e===!0||e===!1,W=e=>{if(ee(e)!=="object")return!1;const t=ye(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},qt=k("Date"),$t=k("File"),Mt=k("Blob"),It=k("FileList"),Ht=e=>re(e)&&v(e.pipe),zt=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||v(e.append)&&((t=ee(e))==="formdata"||t==="object"&&v(e.toString)&&e.toString()==="[object FormData]"))},Jt=k("URLSearchParams"),[Vt,Wt,Kt,Xt]=["ReadableStream","Request","Response","Headers"].map(k),Gt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function H(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,s;if(typeof e!="object"&&(e=[e]),q(e))for(n=0,s=e.length;n<s;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),a=o.length;let c;for(n=0;n<a;n++)c=o[n],t.call(null,e[c],c,e)}}function Ie(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,s;for(;n-- >0;)if(s=r[n],t===s.toLowerCase())return s;return null}const _=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,He=e=>!I(e)&&e!==_;function de(){const{caseless:e}=He(this)&&this||{},t={},r=(n,s)=>{const o=e&&Ie(t,s)||s;W(t[o])&&W(n)?t[o]=de(t[o],n):W(n)?t[o]=de({},n):q(n)?t[o]=n.slice():t[o]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&H(arguments[n],r);return t}const Yt=(e,t,r,{allOwnKeys:n}={})=>(H(t,(s,o)=>{r&&v(s)?e[o]=qe(s,r):e[o]=s},{allOwnKeys:n}),e),Qt=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Zt=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},er=(e,t,r,n)=>{let s,o,a;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)a=s[o],(!n||n(a,e,t))&&!c[a]&&(t[a]=e[a],c[a]=!0);e=r!==!1&&ye(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},tr=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},rr=e=>{if(!e)return null;if(q(e))return e;let t=e.length;if(!Me(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},nr=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ye(Uint8Array)),sr=(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let s;for(;(s=n.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},or=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},ar=k("HTMLFormElement"),ir=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),Ee=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),lr=k("RegExp"),ze=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};H(r,(s,o)=>{let a;(a=t(s,o,e))!==!1&&(n[o]=a||s)}),Object.defineProperties(e,n)},cr=e=>{ze(e,(t,r)=>{if(v(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(v(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},ur=(e,t)=>{const r={},n=s=>{s.forEach(o=>{r[o]=!0})};return q(e)?n(e):n(String(e).split(t)),r},dr=()=>{},fr=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function pr(e){return!!(e&&v(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const mr=e=>{const t=new Array(10),r=(n,s)=>{if(re(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[s]=n;const o=q(n)?[]:{};return H(n,(a,c)=>{const p=r(a,s+1);!I(p)&&(o[c]=p)}),t[s]=void 0,o}}return n};return r(e,0)},hr=k("AsyncFunction"),xr=e=>e&&(re(e)||v(e))&&v(e.then)&&v(e.catch),Je=((e,t)=>e?setImmediate:t?((r,n)=>(_.addEventListener("message",({source:s,data:o})=>{s===_&&o===r&&n.length&&n.shift()()},!1),s=>{n.push(s),_.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",v(_.postMessage)),br=typeof queueMicrotask<"u"?queueMicrotask.bind(_):typeof process<"u"&&process.nextTick||Je,l={isArray:q,isArrayBuffer:$e,isBuffer:_t,isFormData:zt,isArrayBufferView:Lt,isString:Dt,isNumber:Me,isBoolean:Bt,isObject:re,isPlainObject:W,isReadableStream:Vt,isRequest:Wt,isResponse:Kt,isHeaders:Xt,isUndefined:I,isDate:qt,isFile:$t,isBlob:Mt,isRegExp:lr,isFunction:v,isStream:Ht,isURLSearchParams:Jt,isTypedArray:nr,isFileList:It,forEach:H,merge:de,extend:Yt,trim:Gt,stripBOM:Qt,inherits:Zt,toFlatObject:er,kindOf:ee,kindOfTest:k,endsWith:tr,toArray:rr,forEachEntry:sr,matchAll:or,isHTMLForm:ar,hasOwnProperty:Ee,hasOwnProp:Ee,reduceDescriptors:ze,freezeMethods:cr,toObjectSet:ur,toCamelCase:ir,noop:dr,toFiniteNumber:fr,findKey:Ie,global:_,isContextDefined:He,isSpecCompliantForm:pr,toJSONObject:mr,isAsyncFn:hr,isThenable:xr,setImmediate:Je,asap:br};function y(e,t,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}l.inherits(y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:l.toJSONObject(this.config),code:this.code,status:this.status}}});const Ve=y.prototype,We={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{We[e]={value:e}});Object.defineProperties(y,We);Object.defineProperty(Ve,"isAxiosError",{value:!0});y.from=(e,t,r,n,s,o)=>{const a=Object.create(Ve);return l.toFlatObject(e,a,function(p){return p!==Error.prototype},c=>c!=="isAxiosError"),y.call(a,e.message,t,r,n,s),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};const yr=null;function fe(e){return l.isPlainObject(e)||l.isArray(e)}function Ke(e){return l.endsWith(e,"[]")?e.slice(0,-2):e}function Re(e,t,r){return e?e.concat(t).map(function(s,o){return s=Ke(s),!r&&o?"["+s+"]":s}).join(r?".":""):t}function gr(e){return l.isArray(e)&&!e.some(fe)}const wr=l.toFlatObject(l,{},null,function(t){return/^is[A-Z]/.test(t)});function ne(e,t,r){if(!l.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=l.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(b,h){return!l.isUndefined(h[b])});const n=r.metaTokens,s=r.visitor||u,o=r.dots,a=r.indexes,p=(r.Blob||typeof Blob<"u"&&Blob)&&l.isSpecCompliantForm(t);if(!l.isFunction(s))throw new TypeError("visitor must be a function");function d(m){if(m===null)return"";if(l.isDate(m))return m.toISOString();if(!p&&l.isBlob(m))throw new y("Blob is not supported. Use a Buffer instead.");return l.isArrayBuffer(m)||l.isTypedArray(m)?p&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function u(m,b,h){let x=m;if(m&&!h&&typeof m=="object"){if(l.endsWith(b,"{}"))b=n?b:b.slice(0,-2),m=JSON.stringify(m);else if(l.isArray(m)&&gr(m)||(l.isFileList(m)||l.endsWith(b,"[]"))&&(x=l.toArray(m)))return b=Ke(b),x.forEach(function(j,A){!(l.isUndefined(j)||j===null)&&t.append(a===!0?Re([b],A,o):a===null?b:b+"[]",d(j))}),!1}return fe(m)?!0:(t.append(Re(h,b,o),d(m)),!1)}const f=[],g=Object.assign(wr,{defaultVisitor:u,convertValue:d,isVisitable:fe});function N(m,b){if(!l.isUndefined(m)){if(f.indexOf(m)!==-1)throw Error("Circular reference detected in "+b.join("."));f.push(m),l.forEach(m,function(x,w){(!(l.isUndefined(x)||x===null)&&s.call(t,x,l.isString(w)?w.trim():w,b,g))===!0&&N(x,b?b.concat(w):[w])}),f.pop()}}if(!l.isObject(e))throw new TypeError("data must be an object");return N(e),t}function Se(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function ge(e,t){this._pairs=[],e&&ne(e,this,t)}const Xe=ge.prototype;Xe.append=function(t,r){this._pairs.push([t,r])};Xe.toString=function(t){const r=t?function(n){return t.call(this,n,Se)}:Se;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function jr(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ge(e,t,r){if(!t)return e;const n=r&&r.encode||jr;l.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let o;if(s?o=s(t,r):o=l.isURLSearchParams(t)?t.toString():new ge(t,r).toString(n),o){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Oe{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){l.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Ye={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Nr=typeof URLSearchParams<"u"?URLSearchParams:ge,Er=typeof FormData<"u"?FormData:null,Rr=typeof Blob<"u"?Blob:null,Sr={isBrowser:!0,classes:{URLSearchParams:Nr,FormData:Er,Blob:Rr},protocols:["http","https","file","blob","url","data"]},we=typeof window<"u"&&typeof document<"u",pe=typeof navigator=="object"&&navigator||void 0,Or=we&&(!pe||["ReactNative","NativeScript","NS"].indexOf(pe.product)<0),Ar=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Tr=we&&window.location.href||"http://localhost",vr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:we,hasStandardBrowserEnv:Or,hasStandardBrowserWebWorkerEnv:Ar,navigator:pe,origin:Tr},Symbol.toStringTag,{value:"Module"})),O={...vr,...Sr};function Cr(e,t){return ne(e,new O.classes.URLSearchParams,Object.assign({visitor:function(r,n,s,o){return O.isNode&&l.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function kr(e){return l.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Fr(e){const t={},r=Object.keys(e);let n;const s=r.length;let o;for(n=0;n<s;n++)o=r[n],t[o]=e[o];return t}function Qe(e){function t(r,n,s,o){let a=r[o++];if(a==="__proto__")return!0;const c=Number.isFinite(+a),p=o>=r.length;return a=!a&&l.isArray(s)?s.length:a,p?(l.hasOwnProp(s,a)?s[a]=[s[a],n]:s[a]=n,!c):((!s[a]||!l.isObject(s[a]))&&(s[a]=[]),t(r,n,s[a],o)&&l.isArray(s[a])&&(s[a]=Fr(s[a])),!c)}if(l.isFormData(e)&&l.isFunction(e.entries)){const r={};return l.forEachEntry(e,(n,s)=>{t(kr(n),s,r,0)}),r}return null}function Pr(e,t,r){if(l.isString(e))try{return(t||JSON.parse)(e),l.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const z={transitional:Ye,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,o=l.isObject(t);if(o&&l.isHTMLForm(t)&&(t=new FormData(t)),l.isFormData(t))return s?JSON.stringify(Qe(t)):t;if(l.isArrayBuffer(t)||l.isBuffer(t)||l.isStream(t)||l.isFile(t)||l.isBlob(t)||l.isReadableStream(t))return t;if(l.isArrayBufferView(t))return t.buffer;if(l.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Cr(t,this.formSerializer).toString();if((c=l.isFileList(t))||n.indexOf("multipart/form-data")>-1){const p=this.env&&this.env.FormData;return ne(c?{"files[]":t}:t,p&&new p,this.formSerializer)}}return o||s?(r.setContentType("application/json",!1),Pr(t)):t}],transformResponse:[function(t){const r=this.transitional||z.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(l.isResponse(t)||l.isReadableStream(t))return t;if(t&&l.isString(t)&&(n&&!this.responseType||s)){const a=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(a)throw c.name==="SyntaxError"?y.from(c,y.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:O.classes.FormData,Blob:O.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};l.forEach(["delete","get","head","post","put","patch"],e=>{z.headers[e]={}});const Ur=l.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),_r=e=>{const t={};let r,n,s;return e&&e.split(`
`).forEach(function(a){s=a.indexOf(":"),r=a.substring(0,s).trim().toLowerCase(),n=a.substring(s+1).trim(),!(!r||t[r]&&Ur[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Ae=Symbol("internals");function M(e){return e&&String(e).trim().toLowerCase()}function K(e){return e===!1||e==null?e:l.isArray(e)?e.map(K):String(e)}function Lr(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Dr=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ae(e,t,r,n,s){if(l.isFunction(n))return n.call(this,t,r);if(s&&(t=r),!!l.isString(t)){if(l.isString(n))return t.indexOf(n)!==-1;if(l.isRegExp(n))return n.test(t)}}function Br(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function qr(e,t){const r=l.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(s,o,a){return this[n].call(this,t,s,o,a)},configurable:!0})})}let T=class{constructor(t){t&&this.set(t)}set(t,r,n){const s=this;function o(c,p,d){const u=M(p);if(!u)throw new Error("header name must be a non-empty string");const f=l.findKey(s,u);(!f||s[f]===void 0||d===!0||d===void 0&&s[f]!==!1)&&(s[f||p]=K(c))}const a=(c,p)=>l.forEach(c,(d,u)=>o(d,u,p));if(l.isPlainObject(t)||t instanceof this.constructor)a(t,r);else if(l.isString(t)&&(t=t.trim())&&!Dr(t))a(_r(t),r);else if(l.isHeaders(t))for(const[c,p]of t.entries())o(p,c,n);else t!=null&&o(r,t,n);return this}get(t,r){if(t=M(t),t){const n=l.findKey(this,t);if(n){const s=this[n];if(!r)return s;if(r===!0)return Lr(s);if(l.isFunction(r))return r.call(this,s,n);if(l.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=M(t),t){const n=l.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||ae(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let s=!1;function o(a){if(a=M(a),a){const c=l.findKey(n,a);c&&(!r||ae(n,n[c],c,r))&&(delete n[c],s=!0)}}return l.isArray(t)?t.forEach(o):o(t),s}clear(t){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const o=r[n];(!t||ae(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const r=this,n={};return l.forEach(this,(s,o)=>{const a=l.findKey(n,o);if(a){r[a]=K(s),delete r[o];return}const c=t?Br(o):String(o).trim();c!==o&&delete r[o],r[c]=K(s),n[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return l.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=t&&l.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(s=>n.set(s)),n}static accessor(t){const n=(this[Ae]=this[Ae]={accessors:{}}).accessors,s=this.prototype;function o(a){const c=M(a);n[c]||(qr(s,a),n[c]=!0)}return l.isArray(t)?t.forEach(o):o(t),this}};T.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);l.reduceDescriptors(T.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});l.freezeMethods(T);function ie(e,t){const r=this||z,n=t||r,s=T.from(n.headers);let o=n.data;return l.forEach(e,function(c){o=c.call(r,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Ze(e){return!!(e&&e.__CANCEL__)}function $(e,t,r){y.call(this,e??"canceled",y.ERR_CANCELED,t,r),this.name="CanceledError"}l.inherits($,y,{__CANCEL__:!0});function et(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new y("Request failed with status code "+r.status,[y.ERR_BAD_REQUEST,y.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function $r(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Mr(e,t){e=e||10;const r=new Array(e),n=new Array(e);let s=0,o=0,a;return t=t!==void 0?t:1e3,function(p){const d=Date.now(),u=n[o];a||(a=d),r[s]=p,n[s]=d;let f=o,g=0;for(;f!==s;)g+=r[f++],f=f%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),d-a<t)return;const N=u&&d-u;return N?Math.round(g*1e3/N):void 0}}function Ir(e,t){let r=0,n=1e3/t,s,o;const a=(d,u=Date.now())=>{r=u,s=null,o&&(clearTimeout(o),o=null),e.apply(null,d)};return[(...d)=>{const u=Date.now(),f=u-r;f>=n?a(d,u):(s=d,o||(o=setTimeout(()=>{o=null,a(s)},n-f)))},()=>s&&a(s)]}const Y=(e,t,r=3)=>{let n=0;const s=Mr(50,250);return Ir(o=>{const a=o.loaded,c=o.lengthComputable?o.total:void 0,p=a-n,d=s(p),u=a<=c;n=a;const f={loaded:a,total:c,progress:c?a/c:void 0,bytes:p,rate:d||void 0,estimated:d&&c&&u?(c-a)/d:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(f)},r)},Te=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},ve=e=>(...t)=>l.asap(()=>e(...t)),Hr=O.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,O.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(O.origin),O.navigator&&/(msie|trident)/i.test(O.navigator.userAgent)):()=>!0,zr=O.hasStandardBrowserEnv?{write(e,t,r,n,s,o){const a=[e+"="+encodeURIComponent(t)];l.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),l.isString(n)&&a.push("path="+n),l.isString(s)&&a.push("domain="+s),o===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Jr(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Vr(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function tt(e,t,r){let n=!Jr(t);return e&&n||r==!1?Vr(e,t):t}const Ce=e=>e instanceof T?{...e}:e;function D(e,t){t=t||{};const r={};function n(d,u,f,g){return l.isPlainObject(d)&&l.isPlainObject(u)?l.merge.call({caseless:g},d,u):l.isPlainObject(u)?l.merge({},u):l.isArray(u)?u.slice():u}function s(d,u,f,g){if(l.isUndefined(u)){if(!l.isUndefined(d))return n(void 0,d,f,g)}else return n(d,u,f,g)}function o(d,u){if(!l.isUndefined(u))return n(void 0,u)}function a(d,u){if(l.isUndefined(u)){if(!l.isUndefined(d))return n(void 0,d)}else return n(void 0,u)}function c(d,u,f){if(f in t)return n(d,u);if(f in e)return n(void 0,d)}const p={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:c,headers:(d,u,f)=>s(Ce(d),Ce(u),f,!0)};return l.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=p[u]||s,g=f(e[u],t[u],u);l.isUndefined(g)&&f!==c||(r[u]=g)}),r}const rt=e=>{const t=D({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:c}=t;t.headers=a=T.from(a),t.url=Ge(tt(t.baseURL,t.url),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let p;if(l.isFormData(r)){if(O.hasStandardBrowserEnv||O.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((p=a.getContentType())!==!1){const[d,...u]=p?p.split(";").map(f=>f.trim()).filter(Boolean):[];a.setContentType([d||"multipart/form-data",...u].join("; "))}}if(O.hasStandardBrowserEnv&&(n&&l.isFunction(n)&&(n=n(t)),n||n!==!1&&Hr(t.url))){const d=s&&o&&zr.read(o);d&&a.set(s,d)}return t},Wr=typeof XMLHttpRequest<"u",Kr=Wr&&function(e){return new Promise(function(r,n){const s=rt(e);let o=s.data;const a=T.from(s.headers).normalize();let{responseType:c,onUploadProgress:p,onDownloadProgress:d}=s,u,f,g,N,m;function b(){N&&N(),m&&m(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let h=new XMLHttpRequest;h.open(s.method.toUpperCase(),s.url,!0),h.timeout=s.timeout;function x(){if(!h)return;const j=T.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),S={data:!c||c==="text"||c==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:j,config:e,request:h};et(function(F){r(F),b()},function(F){n(F),b()},S),h=null}"onloadend"in h?h.onloadend=x:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(x)},h.onabort=function(){h&&(n(new y("Request aborted",y.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new y("Network Error",y.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let A=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const S=s.transitional||Ye;s.timeoutErrorMessage&&(A=s.timeoutErrorMessage),n(new y(A,S.clarifyTimeoutError?y.ETIMEDOUT:y.ECONNABORTED,e,h)),h=null},o===void 0&&a.setContentType(null),"setRequestHeader"in h&&l.forEach(a.toJSON(),function(A,S){h.setRequestHeader(S,A)}),l.isUndefined(s.withCredentials)||(h.withCredentials=!!s.withCredentials),c&&c!=="json"&&(h.responseType=s.responseType),d&&([g,m]=Y(d,!0),h.addEventListener("progress",g)),p&&h.upload&&([f,N]=Y(p),h.upload.addEventListener("progress",f),h.upload.addEventListener("loadend",N)),(s.cancelToken||s.signal)&&(u=j=>{h&&(n(!j||j.type?new $(null,e,h):j),h.abort(),h=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const w=$r(s.url);if(w&&O.protocols.indexOf(w)===-1){n(new y("Unsupported protocol "+w+":",y.ERR_BAD_REQUEST,e));return}h.send(o||null)})},Xr=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,s;const o=function(d){if(!s){s=!0,c();const u=d instanceof Error?d:this.reason;n.abort(u instanceof y?u:new $(u instanceof Error?u.message:u))}};let a=t&&setTimeout(()=>{a=null,o(new y(`timeout ${t} of ms exceeded`,y.ETIMEDOUT))},t);const c=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(d=>{d.unsubscribe?d.unsubscribe(o):d.removeEventListener("abort",o)}),e=null)};e.forEach(d=>d.addEventListener("abort",o));const{signal:p}=n;return p.unsubscribe=()=>l.asap(c),p}},Gr=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,s;for(;n<r;)s=n+t,yield e.slice(n,s),n=s},Yr=async function*(e,t){for await(const r of Qr(e))yield*Gr(r,t)},Qr=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},ke=(e,t,r,n)=>{const s=Yr(e,t);let o=0,a,c=p=>{a||(a=!0,n&&n(p))};return new ReadableStream({async pull(p){try{const{done:d,value:u}=await s.next();if(d){c(),p.close();return}let f=u.byteLength;if(r){let g=o+=f;r(g)}p.enqueue(new Uint8Array(u))}catch(d){throw c(d),d}},cancel(p){return c(p),s.return()}},{highWaterMark:2})},se=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",nt=se&&typeof ReadableStream=="function",Zr=se&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),st=(e,...t)=>{try{return!!e(...t)}catch{return!1}},en=nt&&st(()=>{let e=!1;const t=new Request(O.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Fe=64*1024,me=nt&&st(()=>l.isReadableStream(new Response("").body)),Q={stream:me&&(e=>e.body)};se&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Q[t]&&(Q[t]=l.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new y(`Response type '${t}' is not supported`,y.ERR_NOT_SUPPORT,n)})})})(new Response);const tn=async e=>{if(e==null)return 0;if(l.isBlob(e))return e.size;if(l.isSpecCompliantForm(e))return(await new Request(O.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(l.isArrayBufferView(e)||l.isArrayBuffer(e))return e.byteLength;if(l.isURLSearchParams(e)&&(e=e+""),l.isString(e))return(await Zr(e)).byteLength},rn=async(e,t)=>{const r=l.toFiniteNumber(e.getContentLength());return r??tn(t)},nn=se&&(async e=>{let{url:t,method:r,data:n,signal:s,cancelToken:o,timeout:a,onDownloadProgress:c,onUploadProgress:p,responseType:d,headers:u,withCredentials:f="same-origin",fetchOptions:g}=rt(e);d=d?(d+"").toLowerCase():"text";let N=Xr([s,o&&o.toAbortSignal()],a),m;const b=N&&N.unsubscribe&&(()=>{N.unsubscribe()});let h;try{if(p&&en&&r!=="get"&&r!=="head"&&(h=await rn(u,n))!==0){let S=new Request(t,{method:"POST",body:n,duplex:"half"}),C;if(l.isFormData(n)&&(C=S.headers.get("content-type"))&&u.setContentType(C),S.body){const[F,V]=Te(h,Y(ve(p)));n=ke(S.body,Fe,F,V)}}l.isString(f)||(f=f?"include":"omit");const x="credentials"in Request.prototype;m=new Request(t,{...g,signal:N,method:r.toUpperCase(),headers:u.normalize().toJSON(),body:n,duplex:"half",credentials:x?f:void 0});let w=await fetch(m);const j=me&&(d==="stream"||d==="response");if(me&&(c||j&&b)){const S={};["status","statusText","headers"].forEach(je=>{S[je]=w[je]});const C=l.toFiniteNumber(w.headers.get("content-length")),[F,V]=c&&Te(C,Y(ve(c),!0))||[];w=new Response(ke(w.body,Fe,F,()=>{V&&V(),b&&b()}),S)}d=d||"text";let A=await Q[l.findKey(Q,d)||"text"](w,e);return!j&&b&&b(),await new Promise((S,C)=>{et(S,C,{data:A,headers:T.from(w.headers),status:w.status,statusText:w.statusText,config:e,request:m})})}catch(x){throw b&&b(),x&&x.name==="TypeError"&&/fetch/i.test(x.message)?Object.assign(new y("Network Error",y.ERR_NETWORK,e,m),{cause:x.cause||x}):y.from(x,x&&x.code,e,m)}}),he={http:yr,xhr:Kr,fetch:nn};l.forEach(he,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Pe=e=>`- ${e}`,sn=e=>l.isFunction(e)||e===null||e===!1,ot={getAdapter:e=>{e=l.isArray(e)?e:[e];const{length:t}=e;let r,n;const s={};for(let o=0;o<t;o++){r=e[o];let a;if(n=r,!sn(r)&&(n=he[(a=String(r)).toLowerCase()],n===void 0))throw new y(`Unknown adapter '${a}'`);if(n)break;s[a||"#"+o]=n}if(!n){const o=Object.entries(s).map(([c,p])=>`adapter ${c} `+(p===!1?"is not supported by the environment":"is not available in the build"));let a=t?o.length>1?`since :
`+o.map(Pe).join(`
`):" "+Pe(o[0]):"as no adapter specified";throw new y("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return n},adapters:he};function le(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new $(null,e)}function Ue(e){return le(e),e.headers=T.from(e.headers),e.data=ie.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ot.getAdapter(e.adapter||z.adapter)(e).then(function(n){return le(e),n.data=ie.call(e,e.transformResponse,n),n.headers=T.from(n.headers),n},function(n){return Ze(n)||(le(e),n&&n.response&&(n.response.data=ie.call(e,e.transformResponse,n.response),n.response.headers=T.from(n.response.headers))),Promise.reject(n)})}const at="1.8.1",oe={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{oe[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const _e={};oe.transitional=function(t,r,n){function s(o,a){return"[Axios v"+at+"] Transitional option '"+o+"'"+a+(n?". "+n:"")}return(o,a,c)=>{if(t===!1)throw new y(s(a," has been removed"+(r?" in "+r:"")),y.ERR_DEPRECATED);return r&&!_e[a]&&(_e[a]=!0,console.warn(s(a," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,a,c):!0}};oe.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function on(e,t,r){if(typeof e!="object")throw new y("options must be an object",y.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let s=n.length;for(;s-- >0;){const o=n[s],a=t[o];if(a){const c=e[o],p=c===void 0||a(c,o,e);if(p!==!0)throw new y("option "+o+" must be "+p,y.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new y("Unknown option "+o,y.ERR_BAD_OPTION)}}const X={assertOptions:on,validators:oe},P=X.validators;let L=class{constructor(t){this.defaults=t,this.interceptors={request:new Oe,response:new Oe}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=D(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:o}=r;n!==void 0&&X.assertOptions(n,{silentJSONParsing:P.transitional(P.boolean),forcedJSONParsing:P.transitional(P.boolean),clarifyTimeoutError:P.transitional(P.boolean)},!1),s!=null&&(l.isFunction(s)?r.paramsSerializer={serialize:s}:X.assertOptions(s,{encode:P.function,serialize:P.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),X.assertOptions(r,{baseUrl:P.spelling("baseURL"),withXsrfToken:P.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let a=o&&l.merge(o.common,o[r.method]);o&&l.forEach(["delete","get","head","post","put","patch","common"],m=>{delete o[m]}),r.headers=T.concat(a,o);const c=[];let p=!0;this.interceptors.request.forEach(function(b){typeof b.runWhen=="function"&&b.runWhen(r)===!1||(p=p&&b.synchronous,c.unshift(b.fulfilled,b.rejected))});const d=[];this.interceptors.response.forEach(function(b){d.push(b.fulfilled,b.rejected)});let u,f=0,g;if(!p){const m=[Ue.bind(this),void 0];for(m.unshift.apply(m,c),m.push.apply(m,d),g=m.length,u=Promise.resolve(r);f<g;)u=u.then(m[f++],m[f++]);return u}g=c.length;let N=r;for(f=0;f<g;){const m=c[f++],b=c[f++];try{N=m(N)}catch(h){b.call(this,h);break}}try{u=Ue.call(this,N)}catch(m){return Promise.reject(m)}for(f=0,g=d.length;f<g;)u=u.then(d[f++],d[f++]);return u}getUri(t){t=D(this.defaults,t);const r=tt(t.baseURL,t.url,t.allowAbsoluteUrls);return Ge(r,t.params,t.paramsSerializer)}};l.forEach(["delete","get","head","options"],function(t){L.prototype[t]=function(r,n){return this.request(D(n||{},{method:t,url:r,data:(n||{}).data}))}});l.forEach(["post","put","patch"],function(t){function r(n){return function(o,a,c){return this.request(D(c||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:a}))}}L.prototype[t]=r(),L.prototype[t+"Form"]=r(!0)});let an=class it{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(s=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](s);n._listeners=null}),this.promise.then=s=>{let o;const a=new Promise(c=>{n.subscribe(c),o=c}).then(s);return a.cancel=function(){n.unsubscribe(o)},a},t(function(o,a,c){n.reason||(n.reason=new $(o,a,c),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new it(function(s){t=s}),cancel:t}}};function ln(e){return function(r){return e.apply(null,r)}}function cn(e){return l.isObject(e)&&e.isAxiosError===!0}const xe={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(xe).forEach(([e,t])=>{xe[t]=e});function lt(e){const t=new L(e),r=qe(L.prototype.request,t);return l.extend(r,L.prototype,t,{allOwnKeys:!0}),l.extend(r,t,null,{allOwnKeys:!0}),r.create=function(s){return lt(D(e,s))},r}const E=lt(z);E.Axios=L;E.CanceledError=$;E.CancelToken=an;E.isCancel=Ze;E.VERSION=at;E.toFormData=ne;E.AxiosError=y;E.Cancel=E.CanceledError;E.all=function(t){return Promise.all(t)};E.spread=ln;E.isAxiosError=cn;E.mergeConfig=D;E.AxiosHeaders=T;E.formToJSON=e=>Qe(l.isHTMLForm(e)?new FormData(e):e);E.getAdapter=ot.getAdapter;E.HttpStatusCode=xe;E.default=E;const{Axios:Rn,AxiosError:Sn,CanceledError:On,isCancel:An,CancelToken:Tn,VERSION:vn,all:Cn,Cancel:kn,isAxiosError:Fn,spread:Pn,toFormData:Un,AxiosHeaders:_n,HttpStatusCode:Ln,formToJSON:Dn,getAdapter:Bn,mergeConfig:qn}=E,un=void 0,dn={apiBaseUrl:un},U=E.create({baseURL:`${dn.apiBaseUrl}/api`,withCredentials:!0,headers:{"Content-Type":"application/json"}});U.interceptors.response.use(e=>e,e=>{var t,r;return((t=e.response)==null?void 0:t.status)===401&&!((r=e.config.url)!=null&&r.includes("/auth/"))&&(window.location.href="/login"),Promise.reject(e)});const ct=R.createContext(null);function fn({children:e}){const[t,r]=R.useState(null),[n,s]=R.useState(!0),o=async()=>{var u;try{const f=await U.get("/auth/me");r(f.data)}catch(f){(((u=f.response)==null?void 0:u.status)===401||!f.response)&&r(null)}finally{s(!1)}};R.useEffect(()=>{o()},[]);const d={user:t,loading:n,login:async(u,f)=>{s(!0);try{const g=await U.post("/auth/login",{username:u,password:f});r(g.data),await o()}finally{s(!1)}},logout:async()=>{s(!0);try{await U.post("/auth/logout")}finally{r(null),s(!1)}},isAdmin:()=>(t==null?void 0:t.role)==="admin"};return i.jsx(ct.Provider,{value:d,children:e})}function J(){const e=R.useContext(ct);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e}function ut(){return i.jsx("div",{className:"min-h-screen flex items-center justify-center bg-grafana-darker",children:i.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-grafana-primary"})})}function pn({children:e}){const{user:t,loading:r}=J(),n=be();return r?i.jsx(ut,{}):t?i.jsx(i.Fragment,{children:e}):i.jsx(G,{to:"/login",state:{from:n.pathname},replace:!0})}function mn({children:e}){const{user:t,loading:r,isAdmin:n}=J();return r?i.jsx("div",{children:"Loading..."}):!t||!n()?i.jsx(G,{to:"/dashboard"}):i.jsx(i.Fragment,{children:e})}function hn(){const{user:e,isAdmin:t,logout:r}=J(),n=Le(),s=be(),[o,a]=R.useState(!1),c=async()=>{await r(),n("/login")},p=({to:d,icon:u,children:f})=>{const g=s.pathname===d;return i.jsxs(pt,{to:d,className:`flex items-center px-4 py-3 rounded-lg transition-all duration-300 ${g?"bg-app-primary text-app-light shadow-lg shadow-app-primary/20":"text-app-muted hover:bg-app-surface hover:text-app-light"}`,onClick:()=>a(!1),children:[i.jsx(u,{className:`w-5 h-5 mr-3 transition-transform duration-300 ${g?"scale-110":""}`}),f]})};return i.jsxs("div",{className:"flex h-screen bg-app-darker",children:[o&&i.jsx("div",{className:"fixed inset-0 bg-app-darker/80 backdrop-blur-sm z-20 lg:hidden animate-fade-in",onClick:()=>a(!1)}),i.jsxs("div",{className:`fixed lg:static inset-y-0 left-0 w-64 bg-app-dark border-r border-app-border transform transition-transform duration-300 ease-in-out z-30 lg:transform-none ${o?"translate-x-0":"-translate-x-full lg:translate-x-0"}`,children:[i.jsxs("div",{className:"h-16 flex items-center justify-between px-4 border-b border-app-border",children:[i.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-app-primary to-app-highlight bg-clip-text text-transparent",children:"Dashboard"}),i.jsx("button",{className:"lg:hidden text-app-muted hover:text-app-light transition-colors duration-200",onClick:()=>a(!1),children:i.jsx(bt,{className:"w-6 h-6"})})]}),i.jsxs("nav",{className:"mt-6 px-3 space-y-2",children:[i.jsx(p,{to:"/dashboard",icon:yt,children:"Dashboard"}),t()&&i.jsx(p,{to:"/users",icon:gt,children:"Users"})]})]}),i.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[i.jsx("header",{className:"h-16 bg-app-dark border-b border-app-border shadow-sm",children:i.jsxs("div",{className:"h-full px-4 flex items-center justify-between",children:[i.jsxs("div",{className:"flex items-center space-x-4",children:[i.jsx("button",{className:"text-app-muted hover:text-app-light transition-colors duration-200 lg:hidden",onClick:()=>a(!0),children:i.jsx(wt,{className:"w-6 h-6"})}),i.jsxs("span",{className:"text-app-light font-medium",children:["Welcome, ",i.jsx("span",{className:"text-app-primary",children:e==null?void 0:e.username})]})]}),i.jsxs("button",{onClick:c,className:"flex items-center px-3 py-2 rounded-lg text-app-muted hover:text-app-light hover:bg-app-surface transition-all duration-200",children:[i.jsx(jt,{className:"w-5 h-5 mr-2"}),i.jsx("span",{className:"hidden sm:inline",children:"Logout"})]})]})}),i.jsx("main",{className:"flex-1 overflow-auto bg-app-darker p-4 md:p-6",children:i.jsx("div",{className:"animate-fade-in",children:i.jsx(ft,{})})})]})]})}function xn(){const[e,t]=R.useState(""),[r,n]=R.useState(""),[s,o]=R.useState(""),[a,c]=R.useState(!1),{login:p,user:d,loading:u}=J(),f=Le(),g=be();if(R.useEffect(()=>{var m;if(!u&&d){const b=((m=g.state)==null?void 0:m.from)||"/dashboard";f(b,{replace:!0})}},[d,u,f,g]),u)return i.jsx(ut,{});const N=async m=>{var b,h;m.preventDefault(),o(""),c(!0);try{await p(e,r)}catch(x){o(((h=(b=x.response)==null?void 0:b.data)==null?void 0:h.error)||"Failed to login"),c(!1)}};return i.jsx("div",{className:"min-h-screen flex items-center justify-center bg-app-darker p-4",children:i.jsx("div",{className:"w-full max-w-md space-y-8 animate-slide-up",children:i.jsxs("div",{className:"bg-app-dark p-8 rounded-2xl shadow-2xl border border-app-border",children:[i.jsxs("div",{className:"text-center mb-8",children:[i.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-app-primary to-app-highlight bg-clip-text text-transparent",children:"Welcome Back"}),i.jsx("p",{className:"mt-2 text-app-muted",children:"Sign in to your account to continue"})]}),i.jsxs("form",{className:"space-y-6",onSubmit:N,children:[s&&i.jsx("div",{className:"bg-app-error/10 border border-app-error text-app-error rounded-lg p-4 text-sm animate-shake",children:s}),i.jsxs("div",{className:"space-y-5",children:[i.jsxs("div",{children:[i.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-app-light mb-2",children:"Username"}),i.jsx("input",{id:"username",type:"text",required:!0,className:"w-full bg-app-surface border border-app-border rounded-lg px-4 py-3 text-app-light placeholder-app-muted focus:outline-none focus:border-app-primary focus:ring-1 focus:ring-app-primary transition-all duration-200",value:e,onChange:m=>t(m.target.value),disabled:a,placeholder:"Enter your username"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-app-light mb-2",children:"Password"}),i.jsx("input",{id:"password",type:"password",required:!0,className:"w-full bg-app-surface border border-app-border rounded-lg px-4 py-3 text-app-light placeholder-app-muted focus:outline-none focus:border-app-primary focus:ring-1 focus:ring-app-primary transition-all duration-200",value:r,onChange:m=>n(m.target.value),disabled:a,placeholder:"Enter your password"})]})]}),i.jsx("button",{type:"submit",className:"w-full bg-app-primary hover:bg-app-secondary text-app-light font-medium py-3 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-app-primary/25 disabled:opacity-50 disabled:cursor-not-allowed",disabled:a,children:a?i.jsxs("span",{className:"flex items-center justify-center",children:[i.jsx("span",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-app-light mr-2"}),"Signing in..."]}):"Sign in"})]})]})})})}function bn(){const{user:e}=J(),t=[{name:"Active Users",value:"2,345",change:"+12.5%",icon:Nt,trend:"up"},{name:"Server Load",value:"67.2%",change:"-2.3%",icon:Et,trend:"down"},{name:"Total Requests",value:"1.2M",change:"+23.1%",icon:Rt,trend:"up"}];return i.jsxs("div",{className:"space-y-6 animate-fade-in",children:[i.jsxs("div",{className:"bg-app-dark p-6 rounded-2xl shadow-xl border border-app-border",children:[i.jsx("h2",{className:"text-2xl md:text-3xl font-bold bg-gradient-to-r from-app-primary to-app-highlight bg-clip-text text-transparent mb-2",children:"Welcome to Your Dashboard"}),i.jsxs("p",{className:"text-app-muted",children:["You are logged in as ",i.jsx("span",{className:"text-app-light font-medium",children:e==null?void 0:e.username})," with role:"," ",i.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-app-primary/20 text-app-primary",children:e==null?void 0:e.role})]})]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(r=>i.jsxs("div",{className:"bg-app-dark p-6 rounded-xl border border-app-border shadow-lg hover:shadow-xl transition-all duration-300 hover:border-app-primary/50",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("div",{className:"p-3 rounded-lg bg-app-primary/10",children:i.jsx(r.icon,{className:"w-6 h-6 text-app-primary"})}),i.jsx("span",{className:`text-sm font-medium ${r.trend==="up"?"text-app-success":"text-app-error"}`,children:r.change})]}),i.jsx("h3",{className:"mt-4 text-2xl font-bold text-app-light",children:r.value}),i.jsx("p",{className:"text-app-muted mt-1",children:r.name})]},r.name))}),i.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[i.jsxs("div",{className:"bg-app-dark p-6 rounded-xl border border-app-border shadow-lg",children:[i.jsx("h3",{className:"text-lg font-semibold text-app-light mb-4",children:"Recent Activity"}),i.jsx("div",{className:"space-y-4",children:[1,2,3].map((r,n)=>i.jsxs("div",{className:"flex items-center space-x-4 p-3 rounded-lg bg-app-surface",children:[i.jsx("div",{className:"w-2 h-2 rounded-full bg-app-primary"}),i.jsxs("div",{className:"flex-1",children:[i.jsx("p",{className:"text-app-light",children:"System update completed"}),i.jsx("p",{className:"text-sm text-app-muted",children:"2 hours ago"})]})]},n))})]}),i.jsxs("div",{className:"bg-app-dark p-6 rounded-xl border border-app-border shadow-lg",children:[i.jsx("h3",{className:"text-lg font-semibold text-app-light mb-4",children:"System Status"}),i.jsx("div",{className:"space-y-4",children:["Database","API","Storage"].map(r=>i.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-app-surface",children:[i.jsx("span",{className:"text-app-light",children:r}),i.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-app-success/20 text-app-success",children:"Operational"})]},r))})]})]})]})}const ce={name:"",username:"",email:"",password:"",role:"viewer"};function yn(){const[e,t]=R.useState([]),[r,n]=R.useState(!0),[s,o]=R.useState(""),[a,c]=R.useState(!1),[p,d]=R.useState(null),[u,f]=R.useState(ce);R.useEffect(()=>{g()},[]);const g=async()=>{var x,w;try{const j=await U.get("/users");t(j.data)}catch(j){o(((w=(x=j.response)==null?void 0:x.data)==null?void 0:w.error)||"Failed to fetch users")}finally{n(!1)}},N=x=>{x?(d(x),f({name:x.name,username:x.username,email:x.email,password:"",role:x.role})):(d(null),f(ce)),c(!0)},m=()=>{c(!1),d(null),f(ce),o("")},b=async x=>{var w,j;x.preventDefault(),o("");try{if(p){const A=Object.entries(u).reduce((S,[C,F])=>(F!==""&&C in u&&(S[C]=F),S),{});await U.put(`/users/${p.id}`,A)}else await U.post("/users",u);m(),g()}catch(A){o(((j=(w=A.response)==null?void 0:w.data)==null?void 0:j.error)||`Failed to ${p?"update":"create"} user`)}},h=async x=>{var w,j;if(confirm("Are you sure you want to delete this user?"))try{await U.delete(`/users/${x}`),g()}catch(A){o(((j=(w=A.response)==null?void 0:w.data)==null?void 0:j.error)||"Failed to delete user")}};return r?i.jsx("div",{className:"h-full flex items-center justify-center",children:i.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-nexus-primary"})}):i.jsxs("div",{className:"space-y-4 md:space-y-6 p-4 md:p-6 bg-nexus-darker min-h-screen",children:[i.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[i.jsx("h2",{className:"text-2xl md:text-3xl font-bold bg-gradient-to-r from-nexus-primary to-nexus-accent bg-clip-text text-transparent",children:"User Management"}),i.jsxs("button",{onClick:()=>N(),className:"bg-nexus-primary hover:bg-nexus-secondary text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center justify-center sm:justify-start shadow-lg hover:shadow-nexus-primary/25",children:[i.jsx(St,{className:"w-5 h-5 mr-2"}),"Add User"]})]}),s&&i.jsx("div",{className:"bg-nexus-error/10 border border-nexus-error text-nexus-error rounded-lg p-3",children:s}),i.jsx("div",{className:"bg-nexus-surface rounded-xl shadow-xl overflow-hidden border border-nexus-primary/10",children:i.jsx("div",{className:"overflow-x-auto",children:i.jsxs("table",{className:"min-w-full divide-y divide-nexus-dark",children:[i.jsx("thead",{className:"bg-nexus-dark",children:i.jsxs("tr",{children:[i.jsx("th",{className:"px-4 md:px-6 py-4 text-left text-xs font-medium text-nexus-muted uppercase tracking-wider",children:"Name"}),i.jsx("th",{className:"px-4 md:px-6 py-4 text-left text-xs font-medium text-nexus-muted uppercase tracking-wider hidden sm:table-cell",children:"Username"}),i.jsx("th",{className:"px-4 md:px-6 py-4 text-left text-xs font-medium text-nexus-muted uppercase tracking-wider hidden md:table-cell",children:"Email"}),i.jsx("th",{className:"px-4 md:px-6 py-4 text-left text-xs font-medium text-nexus-muted uppercase tracking-wider",children:"Role"}),i.jsx("th",{className:"px-4 md:px-6 py-4 text-left text-xs font-medium text-nexus-muted uppercase tracking-wider hidden lg:table-cell",children:"Created At"}),i.jsx("th",{className:"px-4 md:px-6 py-4 text-right text-xs font-medium text-nexus-muted uppercase tracking-wider",children:"Actions"})]})}),i.jsx("tbody",{className:"divide-y divide-nexus-dark",children:e.map(x=>i.jsxs("tr",{className:"hover:bg-nexus-dark/50 transition-colors duration-150",children:[i.jsx("td",{className:"px-4 md:px-6 py-4 whitespace-nowrap text-nexus-light",children:x.name}),i.jsx("td",{className:"px-4 md:px-6 py-4 whitespace-nowrap text-nexus-light hidden sm:table-cell",children:x.username}),i.jsx("td",{className:"px-4 md:px-6 py-4 whitespace-nowrap text-nexus-light hidden md:table-cell",children:x.email}),i.jsx("td",{className:"px-4 md:px-6 py-4 whitespace-nowrap",children:i.jsx("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${x.role==="admin"?"bg-nexus-primary/20 text-nexus-primary":"bg-nexus-accent/20 text-nexus-accent"}`,children:x.role})}),i.jsx("td",{className:"px-4 md:px-6 py-4 whitespace-nowrap text-nexus-muted hidden lg:table-cell",children:new Date(x.created_at).toLocaleDateString()}),i.jsxs("td",{className:"px-4 md:px-6 py-4 whitespace-nowrap text-right space-x-3",children:[i.jsxs("button",{onClick:()=>N(x),className:"text-nexus-primary hover:text-nexus-accent transition-colors duration-150 inline-flex items-center",children:[i.jsx(Ot,{className:"w-4 h-4"}),i.jsx("span",{className:"hidden sm:inline ml-1",children:"Edit"})]}),i.jsxs("button",{onClick:()=>h(x.id),className:"text-nexus-error hover:text-nexus-error/80 transition-colors duration-150 inline-flex items-center",children:[i.jsx(At,{className:"w-4 h-4"}),i.jsx("span",{className:"hidden sm:inline ml-1",children:"Delete"})]})]})]},x.id))})]})})}),a&&i.jsx("div",{className:"fixed inset-0 bg-nexus-darker/80 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:i.jsxs("div",{className:"bg-nexus-surface p-6 rounded-xl shadow-2xl w-full max-w-md border border-nexus-primary/10",children:[i.jsx("h3",{className:"text-xl font-bold mb-6 bg-gradient-to-r from-nexus-primary to-nexus-accent bg-clip-text text-transparent",children:p?"Edit User":"Create New User"}),i.jsxs("form",{onSubmit:b,className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium mb-1 text-nexus-light",children:"Name"}),i.jsx("input",{type:"text",className:"w-full bg-nexus-dark border border-nexus-primary/20 rounded-lg px-4 py-2 text-nexus-light focus:outline-none focus:border-nexus-primary transition-colors duration-200",value:u.name,onChange:x=>f({...u,name:x.target.value}),required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium mb-1 text-nexus-light",children:"Username"}),i.jsx("input",{type:"text",className:"w-full bg-nexus-dark border border-nexus-primary/20 rounded-lg px-4 py-2 text-nexus-light focus:outline-none focus:border-nexus-primary transition-colors duration-200",value:u.username,onChange:x=>f({...u,username:x.target.value}),required:!0})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium mb-1 text-nexus-light",children:"Email"}),i.jsx("input",{type:"email",className:"w-full bg-nexus-dark border border-nexus-primary/20 rounded-lg px-4 py-2 text-nexus-light focus:outline-none focus:border-nexus-primary transition-colors duration-200",value:u.email,onChange:x=>f({...u,email:x.target.value}),required:!0})]}),i.jsxs("div",{children:[i.jsxs("label",{className:"block text-sm font-medium mb-1 text-nexus-light",children:["Password ",p&&"(leave blank to keep current)"]}),i.jsx("input",{type:"password",className:"w-full bg-nexus-dark border border-nexus-primary/20 rounded-lg px-4 py-2 text-nexus-light focus:outline-none focus:border-nexus-primary transition-colors duration-200",value:u.password,onChange:x=>f({...u,password:x.target.value}),required:!p})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium mb-1 text-nexus-light",children:"Role"}),i.jsxs("select",{className:"w-full bg-nexus-dark border border-nexus-primary/20 rounded-lg px-4 py-2 text-nexus-light focus:outline-none focus:border-nexus-primary transition-colors duration-200",value:u.role,onChange:x=>f({...u,role:x.target.value}),children:[i.jsx("option",{value:"viewer",children:"Viewer"}),i.jsx("option",{value:"admin",children:"Admin"})]})]}),i.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[i.jsx("button",{type:"button",onClick:m,className:"px-4 py-2 text-nexus-muted hover:text-nexus-light transition-colors duration-200",children:"Cancel"}),i.jsxs("button",{type:"submit",className:"bg-nexus-primary hover:bg-nexus-secondary text-white px-6 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-nexus-primary/25",children:[p?"Update":"Create"," User"]})]})]})]})})]})}function gn(){return i.jsx(fn,{children:i.jsx(mt,{children:i.jsxs(ht,{children:[i.jsx(B,{path:"/login",element:i.jsx(xn,{})}),i.jsxs(B,{element:i.jsx(pn,{children:i.jsx(hn,{})}),children:[i.jsx(B,{path:"/dashboard",element:i.jsx(bn,{})}),i.jsx(B,{path:"/users",element:i.jsx(mn,{children:i.jsx(yn,{})})})]}),i.jsx(B,{path:"/",element:i.jsx(G,{to:"/dashboard",replace:!0})}),i.jsx(B,{path:"*",element:i.jsx(G,{to:"/dashboard",replace:!0})})]})})})}ue.createRoot(document.getElementById("root")).render(i.jsx(xt.StrictMode,{children:i.jsx(gn,{})}));
