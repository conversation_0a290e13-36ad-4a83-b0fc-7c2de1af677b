import { ExtendedChatMessage } from '../types';

/**
 * Interface for a parsed tool call
 */
export interface ParsedToolCall {
  toolName: string;
  parameters: Record<string, any>;
  originalText: string;
  isContextReadingTool?: boolean;
}

/**
 * Extracts tool calls from AI responses
 * @param text The AI response text to parse
 * @returns The parsed tool call or null if no tool call was found
 */
export const extractToolCall = (text: string): ParsedToolCall | null => {
  try {
    // First, check for read_context tool specifically (most common case)
    if (text.includes('read_context')) {
      // Look for any format of read_context tool call
      const readContextRegex = /TOOL:\s*\{\s*"tool":\s*"read_context"[^}]*\}/i;
      const readContextMatch = text.match(readContextRegex);

      if (readContextMatch) {
        return {
          toolName: 'read_context',
          parameters: {},
          originalText: readContextMatch[0],
          isContextReadingTool: true
        };
      }

      // Alternative format check (with single quotes or different spacing)
      const altReadContextRegex = /TOOL:\s*\{\s*['"]?tool['"]?\s*:\s*['"]?read_context['"]?/i;
      const altReadContextMatch = text.match(altReadContextRegex);

      if (altReadContextMatch) {
        // Find the closing brace by scanning forward
        const startIndex = altReadContextMatch.index || 0;
        let endIndex = startIndex;
        let braceCount = 0;
        let foundOpenBrace = false;

        for (let i = startIndex; i < text.length; i++) {
          if (text[i] === '{') {
            foundOpenBrace = true;
            braceCount++;
          } else if (text[i] === '}') {
            braceCount--;
            if (foundOpenBrace && braceCount === 0) {
              endIndex = i + 1;
              break;
            }
          }
        }

        const originalText = text.substring(startIndex, endIndex);

        return {
          toolName: 'read_context',
          parameters: {},
          originalText,
          isContextReadingTool: true
        };
      }
    }

    // General case - try to parse JSON
    const toolMatch = text.match(/TOOL:\s*(\{[\s\S]*?\})/);
    if (toolMatch && toolMatch[1]) {
      try {
        const jsonStr = toolMatch[1];
        const toolCall = JSON.parse(jsonStr);

        if (toolCall.tool) {
          return {
            toolName: toolCall.tool,
            parameters: toolCall.parameters || {},
            originalText: toolMatch[0],
            isContextReadingTool: toolCall.tool === 'read_context'
          };
        }
      } catch (jsonError) {
        console.log('JSON parsing error, trying alternative approach:', jsonError);

        // If JSON parsing fails, check if it's a read_context tool
        if (toolMatch[0].includes('read_context')) {
          return {
            toolName: 'read_context',
            parameters: {},
            originalText: toolMatch[0],
            isContextReadingTool: true
          };
        }
      }
    }

    return null;
  } catch (e) {
    console.error('Error extracting tool call:', e);
    return null;
  }
};

/**
 * Replaces a tool call in the text with a placeholder
 * @param text The original text
 * @param toolCall The parsed tool call to replace
 * @param replacement The replacement text
 * @returns The text with the tool call replaced
 */
export const replaceToolCall = (
  text: string,
  toolCall: ParsedToolCall,
  replacement: string
): string => {
  return text.replace(toolCall.originalText, replacement);
};

/**
 * Checks if the AI response contains a tool call
 * @param text The AI response text to check
 * @returns True if the text contains a tool call, false otherwise
 */
export const containsToolCall = (text: string): boolean => {
  return text.includes('TOOL:') && extractToolCall(text) !== null;
};

/**
 * Checks if the AI response contains a read_context tool call
 * @param text The AI response text to check
 * @returns True if the text contains a read_context tool call, false otherwise
 */
export const containsReadContextToolCall = (text: string): boolean => {
  // First try the extractToolCall method
  const toolCall = extractToolCall(text);
  if (toolCall !== null && toolCall.isContextReadingTool === true) {
    return true;
  }

  // Fallback: simple string matching for common patterns
  const patterns = [
    /TOOL:\s*{\s*"tool":\s*"read_context"/i,
    /TOOL:\s*{\s*'tool':\s*'read_context'/i,
    /TOOL:\s*{\s*tool:\s*['"]?read_context['"]?/i,
    /read_context.*tool/i,
    /tool.*read_context/i
  ];

  return patterns.some(pattern => pattern.test(text));
};

/**
 * Check if message content contains a runshellcommand tool call
 * @param content Message content to check
 * @returns True if contains runshellcommand tool call
 */
export const containsShellCommandToolCall = (content: string): boolean => {
  if (!content) return false;
  
  // Check for various patterns that indicate a runshellcommand tool call
  const patterns = [
    /TOOL:\s*runshellcommand/i,
    /```\s*runshellcommand\s*```/i,
    /\{\s*"tool":\s*"runshellcommand"/i,
    /\{\s*"name":\s*"runshellcommand"/i,
    // JSON format pattern - enhanced to be more flexible
    /\{\s*"tool":\s*"runshellcommand"\s*,\s*"parameters":\s*\{/i,
    // More flexible JSON pattern
    /\{\s*"tool"\s*:\s*"runshellcommand"/i,
    // Handle cases with different spacing
    /\{\s*"tool"\s*:\s*"runshellcommand"\s*,\s*"parameters"/i,
    // Handle cases where the JSON might be on multiple lines
    /\{\s*"tool"\s*:\s*"runshellcommand"[\s\S]*?"parameters"/i,
    // Handle cases where the tool call might be embedded in markdown
    /```json\s*\{\s*"tool":\s*"runshellcommand"/i,
    // Handle cases with single quotes
    /\{\s*'tool':\s*'runshellcommand'/i,
    // Handle cases with no quotes around tool name
    /\{\s*tool:\s*['"]?runshellcommand['"]?/i
  ];
  
  const hasMatch = patterns.some(pattern => pattern.test(content));
  
  // Debug logging
  if (hasMatch) {
    console.log('🔍 Shell command tool call detected in content:', {
      contentLength: content.length,
      contentPreview: content.substring(0, 200) + '...',
      patterns: patterns.map((pattern, index) => ({
        index,
        matches: pattern.test(content)
      })).filter(p => p.matches)
    });
  } else {
    // Log when no match is found to help debug
    console.log('🔍 No shell command tool call detected in content:', {
      contentLength: content.length,
      contentPreview: content.substring(0, 200) + '...',
      containsRunshellcommand: content.includes('runshellcommand'),
      containsTool: content.includes('"tool"'),
      containsParameters: content.includes('"parameters"')
    });
  }
  
  return hasMatch;
};

/**
 * Extract shell command from tool call content
 * @param content Message content containing the tool call
 * @returns The command string or null if not found
 */
export const extractShellCommand = (content: string): string | null => {
  if (!content) return null;

  console.log('🔧 Attempting to extract shell command from content:', {
    contentLength: content.length,
    contentPreview: content.substring(0, 300) + '...',
    containsRunshellcommand: content.includes('runshellcommand'),
    containsTool: content.includes('"tool"'),
    containsParameters: content.includes('"parameters"'),
    containsCommand: content.includes('"command"')
  });

  try {
    // First, try to find JSON objects that contain runshellcommand tool
    // Use a more robust approach that finds JSON boundaries properly

    // Look for code block with JSON first (most reliable)
    const codeBlockPattern = /```(?:json)?\s*(\{[\s\S]*?"tool":\s*"runshellcommand"[\s\S]*?\})\s*```/i;
    const codeBlockMatch = content.match(codeBlockPattern);
    if (codeBlockMatch && codeBlockMatch[1]) {
      console.log('🔍 Found code block match:', codeBlockMatch[1]);
      try {
        const parsed = JSON.parse(codeBlockMatch[1]);
        if (parsed.tool === 'runshellcommand' && parsed.parameters?.command) {
          console.log('✅ Extracted command from code block:', parsed.parameters.command);
          return parsed.parameters.command;
        }
      } catch (e) {
        console.warn('Failed to parse JSON from code block:', e);
      }
    }

    // Try to find JSON objects in the content using proper JSON parsing
    // Look for potential JSON objects that contain runshellcommand
    const jsonObjectPattern = /\{[^{}]*"tool"[^{}]*"runshellcommand"[^{}]*\}/gi;
    const matches = content.match(jsonObjectPattern);

    if (matches) {
      console.log('🔍 Found JSON object matches:', matches);
      for (const match of matches) {
        try {
          const parsed = JSON.parse(match);
          if (parsed.tool === 'runshellcommand' && parsed.parameters?.command) {
            console.log('✅ Extracted command from JSON object:', parsed.parameters.command);
            return parsed.parameters.command;
          }
        } catch (e) {
          // Try to fix common JSON issues and parse again
          try {
            // Handle escaped quotes and other common issues
            const fixed = match.replace(/\\"/g, '"').replace(/\\\\/g, '\\');
            const parsed = JSON.parse(fixed);
            if (parsed.tool === 'runshellcommand' && parsed.parameters?.command) {
              console.log('✅ Extracted command from fixed JSON:', parsed.parameters.command);
              return parsed.parameters.command;
            }
          } catch (e2) {
            // Continue to next match
          }
        }
      }
    }

    // Try to parse each line as JSON if it looks like a tool call
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('{') && trimmed.includes('runshellcommand')) {
        console.log('🔍 Found potential JSON line:', trimmed);
        try {
          const parsed = JSON.parse(trimmed);
          if (parsed.tool === 'runshellcommand' && parsed.parameters?.command) {
            console.log('✅ Extracted command from line:', parsed.parameters.command);
            return parsed.parameters.command;
          }
        } catch (e) {
          // Continue to next line
        }
      }
    }

    // Last resort: try to find nested JSON objects
    // This handles cases where the JSON might be embedded in other content
    const nestedJsonPattern = /\{[\s\S]*?"tool":\s*"runshellcommand"[\s\S]*?\}/gi;
    const nestedMatches = content.match(nestedJsonPattern);

    if (nestedMatches) {
      console.log('🔍 Found nested JSON matches:', nestedMatches);
      for (const match of nestedMatches) {
        try {
          const parsed = JSON.parse(match);
          if (parsed.tool === 'runshellcommand' && parsed.parameters?.command) {
            console.log('✅ Extracted command from nested JSON:', parsed.parameters.command);
            return parsed.parameters.command;
          }
        } catch (e) {
          // Continue to next match
        }
      }
    }

    // Enhanced: Try to extract command using regex if JSON parsing fails
    const commandPattern = /"command"\s*:\s*"([^"]+)"/;
    const commandMatch = content.match(commandPattern);
    if (commandMatch && commandMatch[1]) {
      console.log('✅ Extracted command using regex fallback:', commandMatch[1]);
      return commandMatch[1];
    }

    // New: Try to find any JSON-like structure that might contain the command
    const anyJsonPattern = /\{[^}]*"command"[^}]*\}/gi;
    const anyJsonMatches = content.match(anyJsonPattern);
    if (anyJsonMatches) {
      console.log('🔍 Found any JSON matches with command:', anyJsonMatches);
      for (const match of anyJsonMatches) {
        try {
          const parsed = JSON.parse(match);
          if (parsed.command) {
            console.log('✅ Extracted command from any JSON:', parsed.command);
            return parsed.command;
          }
        } catch (e) {
          // Try regex extraction from this match
          const commandMatch = match.match(/"command"\s*:\s*"([^"]+)"/);
          if (commandMatch && commandMatch[1]) {
            console.log('✅ Extracted command using regex from any JSON:', commandMatch[1]);
            return commandMatch[1];
          }
        }
      }
    }

    // New: Try to find the command in the entire content using a more flexible approach
    const flexibleCommandPattern = /"command"\s*:\s*"([^"]*?)"/;
    const flexibleMatch = content.match(flexibleCommandPattern);
    if (flexibleMatch && flexibleMatch[1]) {
      console.log('✅ Extracted command using flexible regex:', flexibleMatch[1]);
      return flexibleMatch[1];
    }

    console.warn('❌ Could not extract shell command from content');
    console.log('🔍 Content analysis:', {
      content: content,
      lines: content.split('\n').map((line, index) => ({ index, line: line.trim() }))
    });
    return null;
  } catch (error) {
    console.error('Error extracting shell command:', error);
    return null;
  }
};
