/* Global CSS Variables with default dark theme */
:root {
  /* Main backgrounds */
  --color-bg: #12151e;
  --color-surface: #1e2333;
  --color-surface-light: #2a3147;
  --color-surface-dark: #181d2b;
  
  /* Primary colors */
  --color-primary: #4f8bff;
  --color-primary-light: #7caaff;
  --color-primary-dark: #3267d6;
  --color-primary-translucent: rgba(79, 139, 255, 0.1);
  
  /* Secondary colors */
  --color-secondary: #a855f7;
  --color-secondary-light: #c084fc;
  --color-secondary-dark: #9333ea;
  
  /* Text colors */
  --color-text: #e5e7eb;
  --color-text-secondary: #9ca3af;
  --color-text-muted: #6b7280;
  
  /* Borders */
  --color-border: #2f374f;
  --color-border-light: #3b4666;
  --color-border-subtle: rgba(47, 55, 79, 0.5);
  
  /* Status colors */
  --color-success: #22c55e;
  --color-warning: #fbbf24;
  --color-error: #f87171;
  --color-info: #4f8bff;
  
  /* Chart colors */
  --color-chart-1: #4f8bff;
  --color-chart-2: #a855f7;
  --color-chart-3: #f472b6;
  --color-chart-4: #22c55e;
  --color-chart-5: #fbbf24;
}

/* Light theme */
[data-theme="light"] {
  /* Main backgrounds */
  --color-bg: #f9fafb;
  --color-surface: #ffffff;
  --color-surface-light: #f3f4f6;
  --color-surface-dark: #e5e7eb;
  
  /* Primary colors */
  --color-primary: #2563eb;
  --color-primary-light: #4f8bff;
  --color-primary-dark: #1e40af;
  --color-primary-translucent: rgba(37, 99, 235, 0.08);
  
  /* Secondary colors */
  --color-secondary: #7c3aed;
  --color-secondary-light: #a855f7;
  --color-secondary-dark: #6d28d9;
  
  /* Text colors */
  --color-text: #1f2937;
  --color-text-secondary: #4b5563;
  --color-text-muted: #6b7280;
  
  /* Borders */
  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;
  --color-border-subtle: rgba(229, 231, 235, 0.7);
}

/* Midnight theme */
[data-theme="midnight"] {
  /* Main backgrounds */
  --color-bg: #0b0c1b;
  --color-surface: #15162e;
  --color-surface-light: #1f2142;
  --color-surface-dark: #0d0e24;
  
  /* Primary colors */
  --color-primary: #a855f7;
  --color-primary-light: #c084fc;
  --color-primary-dark: #9333ea;
  --color-primary-translucent: rgba(168, 85, 247, 0.12);
  
  /* Secondary colors */
  --color-secondary: #f472b6;
  --color-secondary-light: #f9a8d4;
  --color-secondary-dark: #ec4899;
  
  /* Text colors */
  --color-text: #e5e7eb;
  --color-text-secondary: #9ca3af;
  --color-text-muted: #6b7280;
  
  /* Borders */
  --color-border: #252552;
  --color-border-light: #333366;
  --color-border-subtle: rgba(37, 37, 82, 0.5);
}

/* Base Styles for App */
html {
  height: 100%;
  background-color: var(--color-bg);
}

body {
  background-color: var(--color-bg);
  color: var(--color-text);
  font-family: 'Poppins', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  transition: background-color 0.3s ease, color 0.3s ease;
  min-height: 100%;

  /* Enhanced text rendering for better sharpness and clarity */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
  font-variant-ligatures: common-ligatures;
}

#root {
  min-height: 100vh;
  background-color: var(--color-bg);
}

/* Apply theme transitions and enhanced text rendering */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;

  /* Universal text sharpening for all elements */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Enhanced text clarity for all text elements */
*,
*::before,
*::after {
  font-feature-settings: "kern" 1;
  font-kerning: normal;
  font-variant-ligatures: common-ligatures;
}

/* Enhanced Typography with Poppins */
h1, h2, h3, h4, h5, h6 {
  color: var(--color-text);
  margin-top: 0;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.025em;
  font-family: 'Poppins', sans-serif;
}

h1 {
  font-size: clamp(1.875rem, 4vw, 2.25rem);
  font-weight: 700;
}

h2 {
  font-size: clamp(1.25rem, 3vw, 1.5rem);
  font-weight: 600;
}

h3 {
  font-size: clamp(1.125rem, 2.5vw, 1.25rem);
  font-weight: 600;
}

h4 {
  font-size: clamp(1rem, 2vw, 1.125rem);
  font-weight: 500;
}

p, span, div {
  color: var(--color-text);
  line-height: 1.6;
  font-weight: 400;
  letter-spacing: 0.01em;
}

small, .text-small {
  color: var(--color-text-secondary);
  font-size: clamp(0.75rem, 1.5vw, 0.875rem);
  font-weight: 400;
}

/* Enhanced Input Styling */
input, select, textarea {
  background-color: var(--color-surface-dark);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: clamp(0.875rem, 2vw, 1rem);
  line-height: 1.5;
  letter-spacing: 0.01em;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-translucent);
}

input:disabled, select:disabled, textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Enhanced Button Styling */
button {
  cursor: pointer;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: clamp(0.875rem, 1.8vw, 1rem);
  letter-spacing: 0.025em;
  transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

button.primary {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

button.primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button.secondary {
  background-color: transparent;
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

button.secondary:hover {
  background-color: var(--color-surface-light);
  border-color: var(--color-border-light);
  transform: translateY(-2px);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Card Styling */
.card {
  background-color: var(--color-surface);
  border-radius: 0.75rem;
  border: 1px solid var(--color-border);
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

/* Table Styling */
table {
  width: 100%;
  border-collapse: collapse;
}

th {
  background-color: var(--color-surface-dark);
  color: var(--color-text-secondary);
  font-weight: 500;
  text-align: left;
  padding: 1rem;
}

td {
  padding: 1rem;
  border-bottom: 1px solid var(--color-border);
  color: var(--color-text);
}

tr:hover {
  background-color: var(--color-surface-light);
}

/* Link Styling */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover, a:active, a:focus {
  color: var(--color-primary-light);
  text-decoration: none !important;
  outline: none !important;
}

/* Status Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.8125rem;
  font-weight: 500;
}

.badge-success {
  background-color: var(--color-success);
  color: white;
}

.badge-warning {
  background-color: var(--color-warning);
  color: white;
}

.badge-error {
  background-color: var(--color-error);
  color: white;
}

.badge-info {
  background-color: var(--color-info);
  color: white;
}

/* Modal Styling */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modal-content {
  background-color: var(--color-surface);
  border-radius: 0.75rem;
  border: 1px solid var(--color-border);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
  padding: 2rem;
  max-width: 90%;
  max-height: 90%;
  overflow-y: auto;
}

/* Force specific elements to use theme colors */
.bg-theme {
  background-color: var(--color-bg) !important;
}

.surface-theme {
  background-color: var(--color-surface) !important;
}

.text-theme {
  color: var(--color-text) !important;
}

.text-primary-theme {
  color: var(--color-primary) !important;
}

.border-theme {
  border-color: var(--color-border) !important;
}
/* Device-specific optimizations for better text clarity */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi) {
  body {
    -webkit-font-smoothing: subpixel-antialiased;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  body {
    font-size: 16px; /* Prevent zoom on iOS */
    line-height: 1.5;
  }

  input, select, textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  button {
    min-height: 44px; /* Better touch targets */
    padding: 0.875rem 1.25rem;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  body {
    font-size: 15px;
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  body {
    font-size: 14px;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 3),
       (min-resolution: 288dpi) {
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}