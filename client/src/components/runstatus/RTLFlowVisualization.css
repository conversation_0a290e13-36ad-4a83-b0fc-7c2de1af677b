.rtl-flow-container {
  width: 100%;
  min-height: 400px;
  padding: 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 1rem;
  position: relative;
  border: 2px solid rgba(0, 0, 0, 0.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 10px rgba(0, 0, 0, 0.03);
  backdrop-filter: blur(10px);
}

.rtl-analysis-status {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.status-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2.5rem;
  border-radius: 1.5rem;
  text-align: center;
  max-width: 450px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(15px);
  border: 2px solid;
  position: relative;
  overflow: hidden;
}

.status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  filter: blur(20px);
  z-index: -1;
}

.status-card.initiating {
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  border-color: #f59e0b;
  color: #ffffff;
}

.status-card.initiated {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  border-color: #10b981;
  color: #ffffff;
}

.status-icon {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.status-card.initiating .status-icon {
  color: #fbbf24;
}

.status-card.initiated .status-icon {
  color: #34d399;
}

.status-card h3 {
  font-size: 1.75rem;
  font-weight: 800;
  margin-bottom: 0.75rem;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.status-card p {
  color: #e2e8f0;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1.4;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.rtl-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding: 1rem;
  background: var(--color-surface-dark);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
}

.rtl-header-main {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--color-primary-10), var(--color-primary-20));
  border-radius: 0.75rem;
  border: 1px solid var(--color-primary-30);
}

.back-button {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.5rem;
  color: var(--color-text);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: var(--color-surface-light);
  transform: translateY(-1px);
}

.version-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.version-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--color-primary);
}

.version-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
  text-transform: uppercase; /* FORCE ALL TEXT TO CAPITAL */
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: var(--color-surface);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  align-self: flex-start;
}

.user-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--color-primary);
}

.username {
  font-weight: 600;
  color: var(--color-text);
  font-size: 0.875rem;
  text-transform: uppercase; /* FORCE ALL TEXT TO CAPITAL */
}

.rtl-versions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: flex-end;
}

.versions-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-muted);
  text-transform: uppercase; /* FORCE ALL TEXT TO CAPITAL */
}

.version-links {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: flex-end;
}

.version-link {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  background: var(--color-surface);
  border: 1px solid var(--color-primary-30);
  border-radius: 0.5rem;
  color: var(--color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px var(--color-shadow);
}

.version-link:hover {
  background: var(--color-primary-10);
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--color-shadow);
}

.version-link-icon {
  width: 1rem;
  height: 1rem;
}

.version-selection-prompt {
  margin-bottom: 2rem;
}

.prompt-card {
  padding: 2rem;
  background: var(--color-surface-dark);
  border-radius: 0.75rem;
  border: 1px solid var(--color-border);
  text-align: center;
}

.prompt-icon {
  width: 3rem;
  height: 3rem;
  color: var(--color-primary);
  margin: 0 auto 1rem;
}

.prompt-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--color-text);
}

.prompt-card p {
  color: var(--color-text-muted);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.version-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--color-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.version-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.version-preview-card {
  padding: 1.5rem;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px var(--color-shadow);
}

.version-preview-card:hover {
  border-color: var(--color-primary);
  box-shadow: 0 8px 16px var(--color-shadow);
  transform: translateY(-5px);
}

.version-preview-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.version-preview-icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--color-primary);
}

.version-preview-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
  margin: 0;
}

.version-preview-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.preview-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.preview-stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-primary);
}

.preview-stat-label {
  font-size: 0.75rem;
  color: var(--color-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.version-preview-action {
  text-align: center;
  padding-top: 0.75rem;
  border-top: 1px solid var(--color-border);
}

.version-preview-action span {
  font-size: 0.875rem;
  color: var(--color-primary);
  font-weight: 500;
}

.version-visualization {
  margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .rtl-header-main {
    flex-direction: column;
    align-items: stretch;
  }
  
  .rtl-versions {
    align-items: stretch;
  }
  
  .version-links {
    justify-content: flex-start;
  }
  
  .version-stats {
    gap: 1rem;
  }
  
  .version-preview-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .rtl-flow-container {
    padding: 0.75rem;
  }
  
  .rtl-header-main {
    padding: 1rem;
  }
  
  .prompt-card {
    padding: 1.5rem;
  }
  
  .version-preview-card {
    padding: 1rem;
  }
}