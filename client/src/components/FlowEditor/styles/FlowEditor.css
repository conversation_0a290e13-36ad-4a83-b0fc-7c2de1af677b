/* Visual Flow Editor Styles - Fixed Version */

/* CSS Custom Properties for better theming */
:root {
  --color-primary-translucent: rgba(79, 70, 229, 0.15);
  --color-secondary-translucent: rgba(139, 69, 19, 0.15);
  --color-success-translucent: rgba(34, 197, 94, 0.15);
  --color-surface-light: rgba(255, 255, 255, 0.05);
  --color-primary-dark: #5b21b6;
}

[data-theme="dark"] {
  --color-primary-translucent: rgba(147, 197, 253, 0.15);
  --color-surface-light: rgba(255, 255, 255, 0.03);
}

[data-theme="midnight"] {
  --color-primary-translucent: rgba(168, 85, 247, 0.15);
  --color-surface-light: rgba(255, 255, 255, 0.02);
}

/* Flow Editor Container */
.flow-editor-container {
  width: 100%;
  height: 100vh;
  background-color: var(--color-bg);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: inherit;
}

.flow-editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Flow Canvas Styles */
.flow-canvas {
  flex: 1;
  position: relative;
  background-color: var(--color-bg);
}

/* React Flow Core Overrides - Prevent white backgrounds */
.react-flow {
  background-color: var(--color-bg) !important;
}

.react-flow__viewport {
  transition: transform 0.2s ease;
  background: transparent !important;
}

.react-flow__pane {
  background: transparent !important;
}

.react-flow__renderer {
  background: transparent !important;
}

/* Node Styles */
.react-flow__node {
  color: var(--color-text);
  font-family: inherit;
  background: transparent !important;
}

/* Fix React Flow default node styling that causes white overlays */
.react-flow__node-input,
.react-flow__node-output,
.react-flow__node-process {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  box-shadow: none !important;
}

/* Ensure selected state doesn't add white backgrounds */
.react-flow__node.selected,
.react-flow__node-input.selected,
.react-flow__node-output.selected,
.react-flow__node-process.selected {
  background: transparent !important;
  outline: none !important;
}

.flow-node {
  background-color: var(--color-surface);
  border: 2px solid var(--color-border);
  border-radius: 12px;
  padding: 20px;
  min-width: 240px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
}

.flow-node:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.flow-node.selected {
  border-color: var(--color-primary);
  transform: scale(1.02);
  box-shadow: 0 0 0 2px var(--color-primary-translucent);
}

.flow-node.dragging {
  transform: rotate(2deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Node Header */
.node-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.node-title {
  color: var(--color-text);
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.node-icon {
  color: var(--color-primary);
  flex-shrink: 0;
}

/* Status Indicator */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: auto;
  flex-shrink: 0;
}

.status-indicator.idle {
  background-color: var(--color-border);
}

.status-indicator.running {
  background-color: var(--color-warning);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-indicator.success {
  background-color: var(--color-success);
}

.status-indicator.error {
  background-color: var(--color-error);
}

/* Node Content */
.node-content {
  color: var(--color-text-secondary);
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
}

/* Node Types */
.input-node {
  border-left: 4px solid var(--color-primary);
}

.process-node {
  border-left: 4px solid var(--color-secondary);
}

.output-node {
  border-left: 4px solid var(--color-success);
}

/* Handle Styles - Override React Flow defaults */
.react-flow__handle {
  width: 12px;
  height: 12px;
  border: 2px solid var(--color-primary);
  background-color: var(--color-surface) !important;
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: none !important;
}

.react-flow__handle:hover {
  width: 16px;
  height: 16px;
  border-width: 3px;
  transform: translate(-2px, -2px);
  background-color: var(--color-surface) !important;
}

.react-flow__handle-connecting {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary-dark);
}

.react-flow__handle-valid {
  background-color: var(--color-success) !important;
  border-color: var(--color-success);
}

.react-flow__handle-invalid {
  background-color: var(--color-error) !important;
  border-color: var(--color-error);
}

/* Specific handle colors for each node type */
.react-flow__node-input .react-flow__handle {
  background-color: var(--color-primary) !important;
  border-color: var(--color-surface);
}

.react-flow__node-process .react-flow__handle {
  background-color: var(--color-secondary) !important;
  border-color: var(--color-surface);
}

.react-flow__node-output .react-flow__handle {
  background-color: var(--color-success) !important;
  border-color: var(--color-surface);
}

/* Enhanced Edge Styles with Laser-like Lighting Effects */
.react-flow__edge-path {
  stroke: var(--color-border);
  stroke-width: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
}

/* Hover Effects - Bright Laser-like Highlighting */
.react-flow__edge:hover .react-flow__edge-path {
  stroke: #00d4ff;
  stroke-width: 4px;
  filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.8))
          drop-shadow(0 0 16px rgba(0, 212, 255, 0.4))
          drop-shadow(0 0 24px rgba(0, 212, 255, 0.2));
  animation: pulse-glow 1.5s ease-in-out infinite alternate;
}

/* Selected State - Enhanced Glow */
.react-flow__edge.selected .react-flow__edge-path {
  stroke: #ff6b35;
  stroke-width: 4px;
  filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.9))
          drop-shadow(0 0 20px rgba(255, 107, 53, 0.5))
          drop-shadow(0 0 30px rgba(255, 107, 53, 0.3));
  animation: selected-pulse 2s ease-in-out infinite alternate;
}

/* Animated Edges - Flow Animation with Glow */
.react-flow__edge.animated .react-flow__edge-path {
  stroke: #10b981;
  stroke-width: 3px;
  stroke-dasharray: 8 4;
  filter: drop-shadow(0 0 6px rgba(16, 185, 129, 0.7))
          drop-shadow(0 0 12px rgba(16, 185, 129, 0.4));
  animation: dashdraw 1s linear infinite, flow-glow 2s ease-in-out infinite alternate;
}

/* Connection Line - Enhanced Preview */
.react-flow__connectionline {
  stroke: var(--color-primary);
  stroke-width: 3px;
  stroke-dasharray: 6 3;
  filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
  animation: connection-preview 1s ease-in-out infinite alternate;
}

/* Enhanced Edge Types */
.react-flow__edge[data-edge-type="success"] .react-flow__edge-path {
  stroke: #22c55e;
  filter: drop-shadow(0 0 6px rgba(34, 197, 94, 0.6));
}

.react-flow__edge[data-edge-type="error"] .react-flow__edge-path {
  stroke: #ef4444;
  filter: drop-shadow(0 0 6px rgba(239, 68, 68, 0.6));
}

.react-flow__edge[data-edge-type="warning"] .react-flow__edge-path {
  stroke: #f59e0b;
  filter: drop-shadow(0 0 6px rgba(245, 158, 11, 0.6));
}

/* Node Connection Highlights */
.react-flow__node.connecting .react-flow__handle {
  background: #00d4ff !important;
  border: 2px solid #00d4ff !important;
  box-shadow: 0 0 12px rgba(0, 212, 255, 0.8);
  animation: handle-pulse 1s ease-in-out infinite alternate;
}

/* Enhanced Handle Styles */
.react-flow__handle {
  background: var(--color-border);
  border: 2px solid var(--color-surface);
  width: 10px;
  height: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.react-flow__handle:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
  transform: scale(1.2);
}

.react-flow__handle.connecting {
  background: #00d4ff;
  border-color: #00d4ff;
  box-shadow: 0 0 12px rgba(0, 212, 255, 0.8);
  animation: handle-pulse 1s ease-in-out infinite alternate;
}

/* Edge Labels with Glow */
.react-flow__edge-text {
  fill: var(--color-text);
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.react-flow__edge:hover .react-flow__edge-text {
  fill: #00d4ff;
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.8);
}

/* Controls */
.react-flow__controls {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.react-flow__controls button {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.react-flow__controls button:hover {
  background: var(--color-surface-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.react-flow__controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* MiniMap */
.react-flow__minimap {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.react-flow__minimap-mask {
  fill: var(--color-primary);
  fill-opacity: 0.2;
  stroke: var(--color-primary);
  stroke-width: 2;
}

/* Background */
.react-flow__background {
  background-color: var(--color-bg);
}

/* Node Palette - made much more compact for wider canvas */
.node-palette {
  width: 220px;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.palette-header {
  padding: 16px 12px;
  border-bottom: 1px solid var(--color-border);
}

.palette-header h3 {
  margin: 0;
  color: var(--color-text);
  font-size: 15px;
  font-weight: 600;
}

.palette-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
}

.palette-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 8px;
  margin-bottom: 6px;
  background: var(--color-surface-dark);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
}

.palette-item:hover {
  background: var(--color-surface-light);
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

.palette-item:active {
  cursor: grabbing;
  transform: scale(0.98);
}

.palette-item-icon {
  color: var(--color-primary);
  flex-shrink: 0;
}

.palette-item-content {
  flex: 1;
}

.palette-item-label {
  display: block;
  color: var(--color-text);
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 2px;
  line-height: 1.2;
}

.palette-item-description {
  display: block;
  color: var(--color-text-secondary);
  font-size: 10px;
  line-height: 1.2;
}

/* Properties Panel - made much more compact for wider canvas */
.properties-panel {
  width: 260px;
  background: var(--color-surface);
  border-left: 1px solid var(--color-border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 12px;
  border-bottom: 1px solid var(--color-border);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--color-text);
  font-size: 15px;
  font-weight: 600;
}

.panel-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.panel-close:hover {
  background: var(--color-surface-light);
  color: var(--color-text);
}

.panel-content {
  flex: 1;
  padding: 16px 12px;
  overflow-y: auto;
}

/* Flow Toolbar */
.flow-toolbar {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-button {
  background: var(--color-surface-dark);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.toolbar-button:hover {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button.primary {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.toolbar-button.primary:hover {
  background: var(--color-primary-dark);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -12;
  }
}

/* Enhanced Glow Animations */
@keyframes pulse-glow {
  0% {
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.8))
            drop-shadow(0 0 16px rgba(0, 212, 255, 0.4))
            drop-shadow(0 0 24px rgba(0, 212, 255, 0.2));
  }
  100% {
    filter: drop-shadow(0 0 12px rgba(0, 212, 255, 1))
            drop-shadow(0 0 24px rgba(0, 212, 255, 0.6))
            drop-shadow(0 0 36px rgba(0, 212, 255, 0.3));
  }
}

@keyframes selected-pulse {
  0% {
    filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.9))
            drop-shadow(0 0 20px rgba(255, 107, 53, 0.5))
            drop-shadow(0 0 30px rgba(255, 107, 53, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 15px rgba(255, 107, 53, 1))
            drop-shadow(0 0 30px rgba(255, 107, 53, 0.7))
            drop-shadow(0 0 45px rgba(255, 107, 53, 0.4));
  }
}

@keyframes flow-glow {
  0% {
    filter: drop-shadow(0 0 6px rgba(16, 185, 129, 0.7))
            drop-shadow(0 0 12px rgba(16, 185, 129, 0.4));
  }
  100% {
    filter: drop-shadow(0 0 10px rgba(16, 185, 129, 0.9))
            drop-shadow(0 0 20px rgba(16, 185, 129, 0.6));
  }
}

@keyframes connection-preview {
  0% {
    filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
    opacity: 0.7;
  }
  100% {
    filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.9));
    opacity: 1;
  }
}

@keyframes handle-pulse {
  0% {
    box-shadow: 0 0 8px rgba(0, 212, 255, 0.6);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 16px rgba(0, 212, 255, 1);
    transform: scale(1.1);
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes contextMenuAppear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .node-palette {
    width: 200px;
  }
  
  .properties-panel {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .flow-editor-main {
    flex-direction: column;
  }
  
  .node-palette,
  .properties-panel {
    width: 100%;
    height: 180px;
  }
  
  .flow-canvas {
    flex: 1;
    min-height: 400px;
  }
  
  .palette-header,
  .panel-header {
    padding: 12px;
  }
  
  .palette-content,
  .panel-content {
    padding: 8px;
  }
}

/* Flow Editor Theme Variables */
:root {
  --color-primary-alpha: rgba(59, 130, 246, 0.1);
  --color-error-alpha: rgba(239, 68, 68, 0.1);
  --color-surface-light: rgba(255, 255, 255, 0.05);
  --color-surface-dark: rgba(0, 0, 0, 0.2);
}

/* Dark theme adjustments */
[data-theme="dark"] {
  --color-primary-alpha: rgba(59, 130, 246, 0.15);
  --color-error-alpha: rgba(239, 68, 68, 0.15);
  --color-surface-light: rgba(255, 255, 255, 0.08);
  --color-surface-dark: rgba(0, 0, 0, 0.3);
}
