/* Processing Toast Notifications */

.processing-toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: none;
}

.processing-toast {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 12px 16px;
  min-width: 280px;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(16px);
  pointer-events: auto;
  animation: slideInRight 0.3s ease;
  transition: all 0.3s ease;
}

.processing-toast:hover {
  transform: translateX(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.16);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Toast Content */
.processing-toast-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.processing-toast-icon {
  font-size: 11px;
  font-weight: 600;
  color: var(--accent-color, #007bff);
  flex-shrink: 0;
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
}

.processing-toast-text {
  flex: 1;
  min-width: 0;
}

.processing-toast-title {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 2px;
  line-height: 1.3;
}

.processing-toast-message {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  word-break: break-word;
}

.processing-toast-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.processing-toast-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #374151;
}

/* Toast Types */
.processing-toast-upload {
  border-left: 4px solid #3b82f6;
}

.processing-toast-processing {
  border-left: 4px solid #f59e0b;
}

.processing-toast-completed {
  border-left: 4px solid #10b981;
}

.processing-toast-error {
  border-left: 4px solid #ef4444;
  background: rgba(254, 242, 242, 0.95);
}

.processing-toast-error .processing-toast-title {
  color: #dc2626;
}

.processing-toast-error .processing-toast-message {
  color: #991b1b;
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .processing-toast {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .processing-toast-title {
    color: #f9fafb;
  }

  .processing-toast-message {
    color: #d1d5db;
  }

  .processing-toast-close {
    color: #9ca3af;
  }

  .processing-toast-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f3f4f6;
  }

  .processing-toast-error {
    background: rgba(69, 10, 10, 0.95);
    border-left-color: #ef4444;
  }

  .processing-toast-error .processing-toast-title {
    color: #fca5a5;
  }

  .processing-toast-error .processing-toast-message {
    color: #f87171;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .processing-toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }

  .processing-toast {
    min-width: auto;
    max-width: none;
    padding: 10px 12px;
  }

  .processing-toast-content {
    gap: 10px;
  }

  .processing-toast-icon {
    font-size: 10px;
    min-width: 50px;
  }

  .processing-toast-title {
    font-size: 13px;
  }

  .processing-toast-message {
    font-size: 11px;
  }

  .processing-toast-close {
    width: 18px;
    height: 18px;
    font-size: 16px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .processing-toast {
    animation: none;
  }

  .processing-toast:hover {
    transform: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .processing-toast {
    border-width: 2px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }

  .processing-toast-title {
    font-weight: 700;
  }

  .processing-toast-close {
    border: 1px solid currentColor;
  }
}
