/* Compact Processing Indicator Styles */

.compact-processing {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 12px 16px;
  margin: 8px 0;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.compact-processing:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Main Progress Container */
.compact-progress-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.compact-progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.compact-progress-status {
  font-size: 11px;
  font-weight: 600;
  color: var(--accent-color, #007bff);
  flex-shrink: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
}

.compact-progress-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.compact-expand-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 10px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.compact-expand-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* Progress Bar */
.compact-progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  min-width: 60px;
}

.compact-progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.5s ease;
  background: #3b82f6;
}

.compact-progress-percent {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  font-weight: 600;
  min-width: 32px;
  text-align: right;
  flex-shrink: 0;
}

/* Expanded Details */
.compact-expanded-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Individual Job Items */
.compact-job-item {
  margin-bottom: 8px;
}

.compact-job-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.compact-job-status {
  font-size: 10px;
  font-weight: 600;
  color: var(--accent-color, #007bff);
  flex-shrink: 0;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  min-width: 50px;
}

.compact-job-name {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.compact-job-progress {
  color: rgba(255, 255, 255, 0.6);
  font-size: 10px;
  font-weight: 600;
  min-width: 28px;
  text-align: right;
  flex-shrink: 0;
}

.compact-job-bar {
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1px;
  overflow: hidden;
}

.compact-job-fill {
  height: 100%;
  border-radius: 1px;
  transition: width 0.5s ease;
  background: #3b82f6;
}

.compact-more-jobs {
  color: rgba(255, 255, 255, 0.5);
  font-size: 10px;
  text-align: center;
  padding: 4px 0;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .compact-processing {
    padding: 10px 12px;
    margin: 6px 0;
  }

  .compact-progress-container {
    gap: 8px;
  }

  .compact-progress-text {
    font-size: 12px;
  }

  .compact-progress-percent {
    font-size: 10px;
    min-width: 28px;
  }

  .compact-progress-bar {
    min-width: 40px;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  .compact-processing {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.15);
  }

  .compact-processing:hover {
    background: rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.25);
  }
}

/* Light Mode Overrides */
@media (prefers-color-scheme: light) {
  .compact-processing {
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.1);
  }

  .compact-processing:hover {
    background: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0.15);
  }

  .compact-progress-text {
    color: rgba(0, 0, 0, 0.8);
  }

  .compact-progress-percent {
    color: rgba(0, 0, 0, 0.6);
  }

  .compact-expand-btn {
    color: rgba(0, 0, 0, 0.5);
  }

  .compact-expand-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    color: rgba(0, 0, 0, 0.8);
  }

  .compact-progress-bar {
    background: rgba(0, 0, 0, 0.1);
  }

  .compact-expanded-details {
    border-top-color: rgba(0, 0, 0, 0.1);
  }

  .compact-job-name {
    color: rgba(0, 0, 0, 0.7);
  }

  .compact-job-progress {
    color: rgba(0, 0, 0, 0.5);
  }

  .compact-job-bar {
    background: rgba(0, 0, 0, 0.1);
  }

  .compact-more-jobs {
    color: rgba(0, 0, 0, 0.4);
  }
}
