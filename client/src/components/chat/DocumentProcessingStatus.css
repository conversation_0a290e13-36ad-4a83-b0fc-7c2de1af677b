/* Document Processing Status Component Styles */

.doc-processing-status {
  background: var(--bg-secondary, #f8f9fa);
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  padding: 16px;
  margin: 12px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Compact Mode Styles */
.doc-processing-compact {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 10px 14px;
  margin: 6px 0;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.doc-processing-compact:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.doc-processing-compact-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.doc-processing-compact-status {
  font-size: 12px;
  font-weight: 600;
  color: var(--accent-color, #007bff);
  flex-shrink: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.doc-processing-compact-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.doc-processing-compact-progress {
  flex: 1;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  min-width: 50px;
  max-width: 80px;
}

.doc-processing-compact-fill {
  height: 100%;
  background: #3b82f6;
  border-radius: 2px;
  transition: width 0.5s ease;
}

.doc-processing-compact-percent {
  color: rgba(255, 255, 255, 0.7);
  font-size: 10px;
  font-weight: 600;
  min-width: 28px;
  text-align: right;
  flex-shrink: 0;
}

/* Header */
.doc-processing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color, #e0e0e0);
}

.doc-processing-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary, #333);
  font-size: 14px;
}

/* Removed doc-processing-icon as it's no longer needed */

.doc-processing-count {
  background: var(--accent-color, #007bff);
  color: white;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.doc-processing-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.doc-processing-offline {
  color: var(--warning-color, #ff9500);
  font-size: 12px;
  cursor: help;
}

.doc-processing-refresh-btn {
  background: none;
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 12px;
  color: var(--text-secondary, #666);
  transition: all 0.2s ease;
}

.doc-processing-refresh-btn:hover:not(:disabled) {
  background: var(--bg-hover, #f0f0f0);
  border-color: var(--accent-color, #007bff);
}

.doc-processing-refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Error Banner */
.doc-processing-error-banner {
  background: var(--error-bg, #fff3f3);
  border: 1px solid var(--error-color, #ff4444);
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--error-color, #ff4444);
  font-size: 13px;
}

.doc-processing-error-close {
  background: none;
  border: none;
  color: var(--error-color, #ff4444);
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  line-height: 1;
}

/* Summary Stats */
.doc-processing-summary {
  margin-bottom: 16px;
}

.doc-processing-summary-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.doc-processing-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.doc-processing-stat-label {
  color: var(--text-secondary, #666);
  font-weight: 500;
}

.doc-processing-stat-value {
  color: var(--text-primary, #333);
  font-weight: 600;
}

.doc-processing-stat-error {
  color: var(--error-color, #ff4444);
}

/* Jobs Section */
.doc-processing-jobs {
  margin-top: 12px;
}

.doc-processing-jobs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary, #666);
}

.doc-processing-jobs-count {
  font-size: 11px;
  color: var(--text-muted, #999);
}

.doc-processing-jobs-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.doc-processing-jobs-more {
  text-align: center;
  padding: 8px;
  color: var(--text-muted, #999);
  font-size: 12px;
  font-style: italic;
  border-top: 1px solid var(--border-light, #f0f0f0);
  margin-top: 8px;
  padding-top: 8px;
}

/* Individual Job */
.doc-processing-job {
  background: white;
  border: 1px solid var(--border-light, #f0f0f0);
  border-radius: 6px;
  padding: 12px;
  transition: box-shadow 0.2s ease;
}

.doc-processing-job:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.doc-processing-job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.doc-processing-job-info {
  flex: 1;
  min-width: 0;
}

.doc-processing-filename {
  display: block;
  font-weight: 500;
  color: var(--text-primary, #333);
  font-size: 13px;
  margin-bottom: 2px;
  word-break: break-word;
}

.doc-processing-status {
  display: inline-block;
  background: var(--bg-muted, #f5f5f5);
  color: var(--text-secondary, #666);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  text-transform: capitalize;
}

.doc-processing-cancel-btn {
  background: var(--error-bg, #fff3f3);
  border: 1px solid var(--error-color, #ff4444);
  color: var(--error-color, #ff4444);
  border-radius: 4px;
  padding: 4px 6px;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  transition: all 0.2s ease;
  margin-left: 8px;
}

.doc-processing-cancel-btn:hover:not(:disabled) {
  background: var(--error-color, #ff4444);
  color: white;
}

.doc-processing-cancel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Progress Bar */
.doc-processing-progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.doc-processing-progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-muted, #f5f5f5);
  border-radius: 3px;
  overflow: hidden;
}

.doc-processing-progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 3px;
}

.doc-processing-progress-queued,
.doc-processing-progress-waiting {
  background: var(--warning-color, #ff9500);
}

.doc-processing-progress-processing {
  background: var(--accent-color, #007bff);
}

.doc-processing-progress-completed {
  background: var(--success-color, #28a745);
}

.doc-processing-progress-failed,
.doc-processing-progress-error {
  background: var(--error-color, #ff4444);
}

.doc-processing-progress-text {
  font-size: 11px;
  color: var(--text-secondary, #666);
  font-weight: 500;
  min-width: 32px;
  text-align: right;
}

/* Messages and Status */
.doc-processing-message {
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin-bottom: 4px;
  font-style: italic;
}

.doc-processing-error {
  font-size: 12px;
  color: var(--error-color, #ff4444);
  margin-bottom: 4px;
  padding: 4px 8px;
  background: var(--error-bg, #fff3f3);
  border-radius: 3px;
}

.doc-processing-queue-position {
  font-size: 11px;
  color: var(--text-muted, #999);
  background: var(--bg-muted, #f5f5f5);
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
}

/* Loading and Empty States */
.doc-processing-loading,
.doc-processing-empty {
  text-align: center;
  padding: 16px;
  color: var(--text-secondary, #666);
  font-size: 13px;
}

.doc-processing-spinner {
  display: inline-block;
  margin-right: 8px;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary, #666);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.doc-processing-refresh-link {
  background: none;
  border: none;
  color: var(--accent-color, #007bff);
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
  margin-left: 8px;
}

.doc-processing-refresh-link:hover {
  color: var(--accent-dark, #0056b3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .doc-processing-status {
    padding: 12px;
    margin: 8px 0;
  }

  .doc-processing-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .doc-processing-controls {
    align-self: flex-end;
  }

  .doc-processing-summary-stats {
    gap: 12px;
  }

  .doc-processing-job-header {
    flex-direction: column;
    gap: 8px;
  }

  .doc-processing-cancel-btn {
    align-self: flex-end;
    margin-left: 0;
  }

  /* Compact mode mobile */
  .doc-processing-compact {
    padding: 8px 10px;
    margin: 4px 0;
  }

  .doc-processing-compact-content {
    gap: 8px;
  }

  .doc-processing-compact-text {
    font-size: 11px;
  }

  .doc-processing-compact-progress {
    min-width: 40px;
    max-width: 60px;
  }

  .doc-processing-compact-percent {
    font-size: 9px;
    min-width: 24px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .doc-processing-status {
    background: var(--bg-secondary-dark, #2d3748);
    border-color: var(--border-color-dark, #4a5568);
    color: var(--text-primary-dark, #e2e8f0);
  }

  .doc-processing-job {
    background: var(--bg-primary-dark, #1a202c);
    border-color: var(--border-light-dark, #2d3748);
  }

  .doc-processing-filename {
    color: var(--text-primary-dark, #e2e8f0);
  }

  .doc-processing-progress-bar {
    background: var(--bg-muted-dark, #4a5568);
  }

  /* Compact mode dark theme */
  .doc-processing-compact {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.15);
  }

  .doc-processing-compact:hover {
    background: rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.25);
  }
}

/* Light Mode Overrides for Compact */
@media (prefers-color-scheme: light) {
  .doc-processing-compact {
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.1);
  }

  .doc-processing-compact:hover {
    background: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0.15);
  }

  .doc-processing-compact-text {
    color: rgba(0, 0, 0, 0.8);
  }

  .doc-processing-compact-percent {
    color: rgba(0, 0, 0, 0.6);
  }

  .doc-processing-compact-progress {
    background: rgba(0, 0, 0, 0.1);
  }
}