{"name": "client", "private": true, "version": "0.0.0", "scripts": {"dev": "react-scripts start", "build": "cross-env DISABLE_ESLINT_PLUGIN=true react-scripts build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "serve -s build"}, "dependencies": {"@chakra-ui/card": "^2.2.0", "@chakra-ui/icons": "^2.2.4", "@chakra-ui/layout": "^2.3.1", "@chakra-ui/react": "^2.8.0", "@chakra-ui/table": "^2.1.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^5.2.0", "@types/dompurify": "^3.0.5", "@types/eventsource": "^1.1.15", "@xyflow/react": "^12.8.2", "axios": "^1.8.4", "date-fns": "^4.1.0", "eventsource": "^2.0.2", "framer-motion": "^12.23.11", "lucide-react": "^0.533.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.61.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.22.1", "react-syntax-highlighter": "^15.5.0", "remark-gfm": "^4.0.1", "uuid": "^11.1.0", "zod": "^4.0.13"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.17", "cross-env": "^7.0.3", "postcss": "^8.4.35", "react-scripts": "^5.0.1", "tailwindcss": "^3.4.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}