#!/usr/bin/env python3.12
"""
Network Scanner for SSH-accessible servers
Python wrapper for bash script to find servers you can SSH into
"""

import subprocess
import tempfile
import os
import json
from typing import List, Dict
from datetime import datetime

class NetworkScanner:
    def __init__(self):
        self.scan_results = []
        self.log_file = None
    
    def scan_network(self, network_range: str, username: str = None) -> List[Dict]:
        """
        Scan network for SSH-accessible servers
        
        Args:
            network_range: Network range (e.g., "172.16.16" or "192.168.1")
            username: SSH username (defaults to current user)
        
        Returns:
            List of dictionaries with server information
        """
        if not username:
            username = os.getenv('USER', 'root')
        
        # Create temporary log file
        self.log_file = tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.log')
        log_path = self.log_file.name
        self.log_file.close()
        
        # Create the bash script
        script_content = self._create_scan_script(network_range, username, log_path)
        
        try:
            # Write script to temporary file
            script_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.sh')
            script_file.write(script_content)
            script_file.close()
            
            # Make script executable
            os.chmod(script_file.name, 0o755)
            
            # Execute the script
            print(f"🔍 Scanning network {network_range}.x for SSH-accessible servers...")
            print(f"👤 Using username: {username}")
            print("⏱️  This may take a few minutes...")
            
            result = subprocess.run(
                [script_file.name],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                timeout=300  # 5 minutes timeout
            )
            
            # Parse results
            self.scan_results = self._parse_scan_results(log_path)
            
            print(f"✅ Scan completed! Found {len(self.scan_results)} SSH-accessible servers")
            
            return self.scan_results
            
        except subprocess.TimeoutExpired:
            print("⏰ Scan timed out after 5 minutes")
            return []
        except Exception as e:
            print(f"❌ Error during scan: {e}")
            return []
        finally:
            # Clean up temporary files
            try:
                os.unlink(script_file.name)
                os.unlink(log_path)
            except:
                pass
    
    def _create_scan_script(self, network_range: str, username: str, log_path: str) -> str:
        """Create the bash script for network scanning"""
        return f"""#!/bin/bash

# Network Scanner for SSH-accessible servers
# Generated by Python NetworkScanner

NETWORK="{network_range}"
USERNAME="{username}"
LOGFILE="{log_path}"

# Clear log file
> "$LOGFILE"

SUCCESS_COUNT=0

echo "🔍 Scanning for SSH-accessible servers on ${{NETWORK}}.x..."
echo "👤 Using username: $USERNAME"
echo "⏱️  This may take a few minutes..."
echo "----------------------------------------"

for i in {{1..254}}; do
    IP="${{NETWORK}}.$i"
    
    # Skip if it's the current machine
    if [[ "$IP" == "$(hostname -I | awk '{{print $1}}')" ]]; then
        continue
    fi
    
    # Test SSH connection (non-interactive)
    if ssh -o ConnectTimeout=3 \\
           -o BatchMode=yes \\
           -o StrictHostKeyChecking=no \\
           -o UserKnownHostsFile=/dev/null \\
           $USERNAME@$IP \\
           "echo OK" &>/dev/null; then

        # Success: Get hostname and OS
        HOSTNAME=$(ssh -o ConnectTimeout=3 $USERNAME@$IP hostname 2>/dev/null | tr -d '\\r')
        OS=$(ssh -o ConnectTimeout=3 $USERNAME@$IP 'grep PRETTY_NAME /etc/os-release 2>/dev/null | cut -d"\\"" -f2' | tr -d '\\r')
        
        # Get additional system info
        CPU_INFO=$(ssh -o ConnectTimeout=3 $USERNAME@$IP 'nproc 2>/dev/null || echo "Unknown"')
        MEM_INFO=$(ssh -o ConnectTimeout=3 $USERNAME@$IP 'free -h 2>/dev/null | grep "^Mem:" | awk "{{print \\$2}}" || echo "Unknown"')

        echo "✅ SSH OK ✅ $IP | Host: $HOSTNAME | OS: $OS"
        echo "$IP,$HOSTNAME,$OS,$CPU_INFO,$MEM_INFO" >> "$LOGFILE"
        ((SUCCESS_COUNT++))

    fi
done

echo "----------------------------------------"
echo "✅ Success! You can SSH into $SUCCESS_COUNT server(s)."
echo "📄 Full list saved to: $LOGFILE"

# Show summary
echo
echo "📋 Reachable Servers:"
if [[ -s "$LOGFILE" ]]; then
    awk -F',' '{{printf "  %-15s %-20s %s\\n", $1, $2, $3}}' "$LOGFILE"
else
    echo "  No servers found"
fi
"""
    
    def _parse_scan_results(self, log_path: str) -> List[Dict]:
        """Parse the scan results from log file"""
        results = []
        
        try:
            with open(log_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and ',' in line:
                        parts = line.split(',')
                        if len(parts) >= 3:
                            server_info = {
                                'ip': parts[0],
                                'hostname': parts[1] if parts[1] else 'Unknown',
                                'os': parts[2] if parts[2] else 'Unknown',
                                'cpu_cores': parts[3] if len(parts) > 3 and parts[3] else 'Unknown',
                                'memory': parts[4] if len(parts) > 4 and parts[4] else 'Unknown',
                                'ssh_accessible': True,
                                'discovered_at': datetime.now().isoformat()
                            }
                            results.append(server_info)
        except Exception as e:
            print(f"❌ Error parsing scan results: {e}")
        
        return results
    
    def get_scan_summary(self) -> Dict:
        """Get summary of scan results"""
        if not self.scan_results:
            return {
                'total_servers': 0,
                'ssh_accessible': 0,
                'os_distribution': {},
                'scan_time': None
            }
        
        os_distribution = {}
        for server in self.scan_results:
            os_name = server.get('os', 'Unknown')
            os_distribution[os_name] = os_distribution.get(os_name, 0) + 1
        
        return {
            'total_servers': len(self.scan_results),
            'ssh_accessible': len([s for s in self.scan_results if s.get('ssh_accessible', False)]),
            'os_distribution': os_distribution,
            'scan_time': datetime.now().isoformat()
        }
    
    def quick_scan(self, network_range: str, username: str = None, max_ips: int = 10, start_ip: int = 1) -> List[Dict]:
        """
        Quick scan method for compatibility with QuickNetworkScanner
        This is a wrapper around scan_network for compatibility
        """
        print(f"🔍 Quick scanning network {network_range}.x (IPs {start_ip}-{start_ip + max_ips - 1})...")
        return self.scan_network(network_range, username)

def scan_network_range(network_range: str, username: str = None) -> List[Dict]:
    """Convenience function to scan a network range"""
    scanner = NetworkScanner()
    return scanner.scan_network(network_range, username)

if __name__ == "__main__":
    # Test the scanner
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python3 network_scanner.py <network_range> [username]")
        print("Example: python3 network_scanner.py 172.16.16 root")
        sys.exit(1)
    
    network = sys.argv[1]
    username = sys.argv[2] if len(sys.argv) > 2 else None
    
    scanner = NetworkScanner()
    results = scanner.scan_network(network, username)
    
    print(f"\n📊 Scan Results:")
    print(f"Total servers found: {len(results)}")
    
    for server in results:
        print(f"  {server['ip']} - {server['hostname']} ({server['os']})") 