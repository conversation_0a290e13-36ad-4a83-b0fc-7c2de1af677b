{"name": "text-processor-api", "version": "1.0.0", "description": "API server for text extraction services", "main": "server.js", "scripts": {"start": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "morgan": "^1.10.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "compression": "^1.7.4", "body-parser": "^1.20.2", "uuid": "^9.0.1"}, "engines": {"node": ">=18.0.0"}, "author": "ProductDemo Team", "license": "UNLICENSED", "private": true}