{"name": "dir-create-api", "version": "1.0.0", "description": "DIR Create Module API Server for remote VLSI directory structure creation", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "morgan": "^1.10.0", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.7.0", "uuid": "^9.0.0", "body-parser": "^1.20.2"}, "devDependencies": {"nodemon": "^2.0.22"}, "keywords": ["vlsi", "eda", "directory", "structure", "mcp", "remote", "execution"], "author": "PinnacleAI", "license": "MIT"}