FROM python:3.9-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set the working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy the requirements file
COPY python/RUN_STATUS/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt


RUN pip install psycopg2-binary==2.9.7
# Copy the application code
COPY python/RUN_STATUS/ .

# Create a non-root user for security
RUN useradd --create-home --shell /bin/bash runstatus
RUN chown -R runstatus:runstatus /app
USER runstatus

# Expose the port the app runs on
EXPOSE 5003

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:5003/health')" || exit 1

# Command to run the application
CMD ["python", "app.py"]