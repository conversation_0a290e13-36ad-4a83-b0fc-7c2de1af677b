FROM python:3.9-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set the working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    libhdf5-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Copy the requirements file
COPY python/PREDICTION-MODULE/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY python/PREDICTION-MODULE/ .

# Create a non-root user for security
RUN useradd --create-home --shell /bin/bash prediction
RUN chown -R prediction:prediction /app
USER prediction

# Expose the port the app runs on
EXPOSE 8088

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:8088/')" || exit 1

# Command to run the application
CMD ["uvicorn", "prediction:app", "--host", "0.0.0.0", "--port", "8088"]