#version: '3.8'

services:
  chromadb:
    image: chromadb/chroma:latest
    container_name: productdemo-chromadb
    ports:
      - "${CHROMADB_HOST_PORT:-8001}:${CHROMADB_CONTAINER_PORT:-8000}"
    volumes:
      - ./DATA/chroma_data:/chroma/chroma
    environment:
      - ALLOW_RESET=true
      - CHROMA_SERVER_CORS_ALLOW_ORIGINS=*
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_PORT=${CHROMADB_CONTAINER_PORT:-8000}
    restart: unless-stopped
    networks:
      - productdemo-network

  redis:
    image: redis:7-alpine
    container_name: productdemo-redis
    ports:
      - "${REDIS_HOST_PORT:-6379}:${REDIS_CONTAINER_PORT:-6379}"
    volumes:
      - redis_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - productdemo-network

  embedding-service:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.embedding-service
    container_name: productdemo-embedding-service
    ports:
      - "${EMBEDDING_HOST_PORT:-3579}:${EMBEDDING_CONTAINER_PORT:-3579}"
    volumes:
      - embedding_models:/app/models
      - ../conf:/app/conf
    environment:
      - OLLAMA_HOST=${HOST_MACHINE_IP:-************}
    restart: unless-stopped
    networks:
      - productdemo-network

  doc-workers:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.workers
    container_name: productdemo-doc-workers
    depends_on:
      - redis
      - chromadb
      - embedding-service
      - text-processor
      - mcp-orchestrator
    volumes:
      - ../DATA:/app/DATA
      - ../python:/app/python
      - ../conf:/app/conf
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=${REDIS_CONTAINER_PORT:-6379}
      - CHROMADB_URL=http://chromadb:${CHROMADB_CONTAINER_PORT:-8000}
      - EMBEDDING_SERVICE_URL=http://embedding-service:${EMBEDDING_CONTAINER_PORT:-3579}
      - TEXT_PROCESSOR_URL=http://text-processor:${TEXT_PROCESSOR_PORT:-3580}
      - IMAGE_PROCESSOR_URL=http://image-processor:8430
      # Use host IP for PostgreSQL and Ollama
      - DATABASE_HOST=${HOST_MACHINE_IP:-************}
      - OLLAMA_HOST=${HOST_MACHINE_IP:-************}
    restart: unless-stopped
    networks:
      - productdemo-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  text-processor:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.text-processor
    container_name: productdemo-text-processor
    ports:
      - "${TEXT_PROCESSOR_PORT:-3580}:3580"
    volumes:
      - ../DATA:/app/data
    restart: unless-stopped
    networks:
      - productdemo-network
    tmpfs:
      - /tmp/uploads:exec

  mcp-orchestrator:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.mcp-orchestrator
    container_name: productdemo-mcp-orchestrator
    ports:
      - "${MCP_ORCHESTRATOR_PORT:-3581}:3581"
    volumes:
      - ../python:/app/python
    restart: unless-stopped
    networks:
      - productdemo-network

  dir-create-module:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.dir-create-module
    container_name: productdemo-dir-create-module
    ports:
      - "${DIR_CREATE_PORT:-3582}:3582"
    volumes:
      - ../python:/app/python
    restart: unless-stopped
    networks:
      - productdemo-network

  image-processor:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.image-processor
    container_name: productdemo-image-processor
    ports:
      - "${IMAGE_PROCESSOR_PORT:-8430}:8430"
    volumes:
      - ../DATA:/app/data
      - ../python:/app/python
    environment:
      - IMAGE_PROCESSOR_HOST=0.0.0.0
      - IMAGE_PROCESSOR_PORT=8430
      - IMAGE_PROCESSOR_DATA_DIR=/app/data
    user: "999:999"
    restart: unless-stopped
    networks:
      - productdemo-network

  app:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.app
    container_name: productdemo-app
    ports:
      - "${APP_PORT:-5641}:5641"
    volumes:
      - ../DATA:/app/DATA
      - ../logs:/app/logs
      - ../conf:/app/conf
      - ../assets:/app/assets
    depends_on:
      - redis
      - chromadb
      - embedding-service
      - text-processor
      - mcp-orchestrator
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PORT=${REDIS_CONTAINER_PORT:-6379}
      - CHROMADB_URL=http://chromadb:${CHROMADB_CONTAINER_PORT:-8000}
      - EMBEDDING_SERVICE_URL=http://embedding-service:${EMBEDDING_CONTAINER_PORT:-3579}
      - TEXT_PROCESSOR_URL=http://text-processor:${TEXT_PROCESSOR_PORT:-3580}
      - MCP_ORCHESTRATOR_URL=http://mcp-orchestrator:${MCP_ORCHESTRATOR_PORT:-3581}
      - IMAGE_PROCESSOR_URL=http://image-processor:8430
      - PREDICTION_SERVICE_URL=http://prediction:8088
      # Use host IP for PostgreSQL and Ollama
      - DATABASE_HOST=${HOST_MACHINE_IP:-************}
      - OLLAMA_HOST=${HOST_MACHINE_IP:-************}
    restart: unless-stopped
    networks:
      - productdemo-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  chat2sql:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.chat2sql
    container_name: productdemo-chat2sql
    ports:
      - "${CHAT2SQL_PORT:-5000}:5000"
    volumes:
      - ../conf/config.ini:/conf/config.ini
    environment:
      - OLLAMA_HOST=host.docker.internal
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    networks:
      - productdemo-network

  runstatus:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.runstatus
    container_name: productdemo-runstatus
    env_file:
      - .env
    ports:
      - "${RUNSTATUS_PORT:-5003}:5003"
    volumes:
      - ../conf/config.ini:/conf/config.ini
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    networks:
      - productdemo-network

  prediction:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.prediction
    container_name: productdemo-prediction
    ports:
      - "${PREDICTION_PORT:-8088}:8088"
    volumes:
      - ../conf/config.ini:/conf/config.ini
    environment:
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_PORT=${DATABASE_PORT}
      - DATABASE_NAME=${DATABASE_NAME}
      - DATABASE_USER=${DATABASE_USER}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    networks:
      - productdemo-network

  resource-server:
    build:
      context: ..
      dockerfile: Docker/Dockerfile.resource-server
    container_name: productdemo-resource-server
    ports:
      - "${RESOURCE_MONITOR_PORT:-8005}:8005"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - productdemo-network

      
volumes:
  redis_data:
  embedding_models:

networks:
  productdemo-network:
    external: true
    name: docker_productdemo-network

