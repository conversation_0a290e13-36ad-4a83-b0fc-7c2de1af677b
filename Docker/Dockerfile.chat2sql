FROM python:3.9-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Set the working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy the requirements file
COPY python/CHAT2SQL-MODULE/requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY python/CHAT2SQL-MODULE/backend.py .

# Create a non-root user for security
RUN useradd --create-home --shell /bin/bash chat2sql
RUN chown -R chat2sql:chat2sql /app
USER chat2sql

# Expose the port the app runs on
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python -c "import requests; requests.get('http://localhost:5000/')" || exit 1

# Command to run the application
CMD ["uvicorn", "backend:app", "--host", "0.0.0.0", "--port", "5000"]