# Docker Port Configuration
# ChromaDB ports (host:container)
CHROMADB_HOST_PORT=8001
CHROMADB_CONTAINER_PORT=8000

# Redis ports (host:container)
REDIS_HOST_PORT=6379
REDIS_CONTAINER_PORT=6379
REDIS_PASSWORD=

# Redis Configuration (for application connections)
REDIS_HOST=redis
REDIS_PORT=6379

# Document Queue Configuration
DOC_WORKER_CONCURRENCY=3
QUEUE_MAX_JOBS_PER_WORKER=10
QUEUE_MAX_RETRIES=3

# ChromaDB Configuration (for application connections)
CHROMADB_HOST=chromadb
CHROMADB_PORT=8000

# Image Processing Configuration
TESSDATA_PREFIX=/usr/share/tesseract-ocr/5/tessdata/
TESSERACT_CMD=/usr/bin/tesseract
IMAGE_MIN_SIZE_KB=5
IMAGE_MIN_WIDTH=50
IMAGE_MIN_HEIGHT=50
OCR_CONFIG=--oem 3 --psm 6

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info

# Host Machine IP Configuration
# Change this to your server's IP address for development
HOST_MACHINE_IP=************

# Resource Monitor Configuration
RESOURCE_MONITOR_PORT=8005
RESOURCE_MONITOR_URL=http://resource-server:8005

# Database Configuration (if needed by workers)
DATABASE_HOST=${HOST_MACHINE_IP}
DATABASE_PORT=5432
DATABASE_NAME=copilot
DATABASE_USER=postgres
DATABASE_PASSWORD=root

# Chat2SQL Configuration
CHAT2SQL_HOST_PORT=5000
OLLAMA_PROTOCOL=http
OLLAMA_HOST=${HOST_MACHINE_IP}
OLLAMA_PORT=11434
OLLAMA_MODEL=mistral

# PostgreSQL Configuration for Chat2SQL (use same as main app)
POSTGRES_HOST=${HOST_MACHINE_IP}
POSTGRES_PORT=5432
POSTGRES_DB=copilot
POSTGRES_USER=postgres
POSTGRES_PASSWORD=root