node_modules
client/node_modules
.env
client/.env
.DS_Store

# DATA directory - Keep folder structure but ignore contents
/DATA/*
!/DATA/
!/DATA/.gitkeep
!/DATA/README.md

# Keep subdirectory structure but ignore their contents
!/DATA/collections/
/DATA/collections/*
!/DATA/collections/.gitkeep

!/DATA/input/
/DATA/input/*
!/DATA/input/.gitkeep

!/DATA/output/
/DATA/output/*
!/DATA/output/.gitkeep

!/DATA/uploads/
/DATA/uploads/*
!/DATA/uploads/.gitkeep

!/DATA/documents/
/DATA/documents/*
!/DATA/documents/.gitkeep

!/DATA/embeddings/
/DATA/embeddings/*
!/DATA/embeddings/.gitkeep

!/DATA/vector_store/
/DATA/vector_store/*
!/DATA/vector_store/.gitkeep

!/DATA/chroma_data/
/DATA/chroma_data/*
!/DATA/chroma_data/.gitkeep

logs/
logs/combined.log
logs/error.log

node_modules/
client/node_modules/

python/venv/
python/.venv

venv/
.venv/

python/CHATSQL-MODULE/venv/
python/CHATSQL-MODULE/.venv
# Python cache and virtual environments
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/
test_venv/
*/test_venv/
python/*/test_venv/
python/DIR_CREATE_MODULE/test_venv/

# Client build artifacts
client/build/
client/dist/
*.tsbuildinfo

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

python/DIR_CREATE_MODULE/test_venv