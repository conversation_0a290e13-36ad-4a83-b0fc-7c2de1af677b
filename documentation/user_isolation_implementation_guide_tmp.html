<!DOCTYPE html>
<html>

<head>
    <title>user_isolation_implementation_guide.md</title>
    <meta http-equiv="Content-type" content="text/html;charset=UTF-8">
    
<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

html,footer,header{
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Custom MD PDF CSS
 */
html,footer,header{
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";

 }
body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>
<link rel="stylesheet" href="file:///home/<USER>/Desktop/c2s_integrate/R%3A%5C2.Travail%5C1.Enseignement%5CCours%5C_1.Outils%5C2.Developpement%5C1.SCSS%5Cmain.css" type="text/css"><link rel="stylesheet" href="file:///home/<USER>/Desktop/c2s_integrate/D%3A%5Crdaros%5CCours%5C_1.Outils%5C2.Developpement%5C1.SCSS%5Cmain.css" type="text/css">
</head>

<body>
    <h1 id="%F0%9F%94%92-user-isolation-implementation-guide">🔒 User Isolation Implementation Guide</h1>
<h2 id="%F0%9F%93%8B-overview">📋 <strong>Overview</strong></h2>
<p>This document explains how <strong>complete user isolation</strong> was implemented in the RAG (Retrieval-Augmented Generation) system, ensuring that each user's documents, embeddings, and RAG capabilities are completely separate from other users.</p>
<h2 id="%F0%9F%8E%AF-user-isolation-requirements">🎯 <strong>User Isolation Requirements</strong></h2>
<h3 id="core-principles"><strong>Core Principles:</strong></h3>
<ol>
<li><strong>Data Separation</strong>: Each user's documents must be stored separately</li>
<li><strong>Search Isolation</strong>: Users can only search their own documents</li>
<li><strong>RAG Toggle Independence</strong>: Each user's RAG availability is independent</li>
<li><strong>Session Management</strong>: User-specific session data handling</li>
<li><strong>Security</strong>: No cross-user data leakage</li>
</ol>
<hr>
<h2 id="%F0%9F%8F%97%EF%B8%8F-architecture-overview">🏗️ <strong>Architecture Overview</strong></h2>
<h3 id="before-user-isolation"><strong>Before User Isolation:</strong></h3>
<pre class="hljs"><code><div>┌─────────────────┐
│   Single Store  │
├─────────────────┤
│ All Users' Docs │
│ Mixed Together  │
│ No Separation   │
└─────────────────┘
</div></code></pre>
<h3 id="after-user-isolation"><strong>After User Isolation:</strong></h3>
<pre class="hljs"><code><div>┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User A Store  │    │   User B Store  │    │   User C Store  │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ User A Docs     │    │ User B Docs     │    │ User C Docs     │
│ User A Sessions │    │ User B Sessions │    │ User C Sessions │
│ User A RAG      │    │ User B RAG      │    │ User C RAG      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
</div></code></pre>
<hr>
<h2 id="%F0%9F%94%A7-implementation-components">🔧 <strong>Implementation Components</strong></h2>
<h3 id="1-chromadb-collection-isolation"><strong>1. ChromaDB Collection Isolation</strong></h3>
<h4 id="collection-naming-strategy"><strong>Collection Naming Strategy:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// User-specific collection names</span>
<span class="hljs-keyword">const</span> getCollectionName = <span class="hljs-function">(<span class="hljs-params">userId</span>) =&gt;</span> {
  <span class="hljs-keyword">return</span> <span class="hljs-string">`user_<span class="hljs-subst">${userId.replace(<span class="hljs-regexp">/-/g</span>, <span class="hljs-string">'_'</span>)}</span>_docs`</span>;
};

<span class="hljs-comment">// Example:</span>
<span class="hljs-comment">// User ID: "65642ced-1986-4579-90c9-d066724f987c"</span>
<span class="hljs-comment">// Collection: "user_65642ced_1986_4579_90c9_d066724f987c_docs"</span>
</div></code></pre>
<h4 id="collection-creation-logic"><strong>Collection Creation Logic:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// src/services/vectorStoreService.js</span>
<span class="hljs-keyword">async</span> getUserCollection(userId) {
  <span class="hljs-keyword">if</span> (!userId) {
    <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">'userId is required for user isolation'</span>);
  }

  <span class="hljs-keyword">const</span> collectionName = <span class="hljs-string">`user_<span class="hljs-subst">${userId.replace(<span class="hljs-regexp">/-/g</span>, <span class="hljs-string">'_'</span>)}</span>_docs`</span>;
  
  <span class="hljs-keyword">try</span> {
    <span class="hljs-comment">// Try to get existing collection</span>
    <span class="hljs-keyword">const</span> collection = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.chromaClient.getCollection({
      <span class="hljs-attr">name</span>: collectionName
    });
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`Retrieved existing ChromaDB collection: <span class="hljs-subst">${collectionName}</span>`</span>);
    <span class="hljs-keyword">return</span> collection;
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-comment">// Create new collection if it doesn't exist</span>
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`Creating new ChromaDB collection: <span class="hljs-subst">${collectionName}</span>`</span>);
    <span class="hljs-keyword">const</span> collection = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.chromaClient.createCollection({
      <span class="hljs-attr">name</span>: collectionName,
      <span class="hljs-attr">metadata</span>: {
        <span class="hljs-attr">userId</span>: userId,
        <span class="hljs-attr">createdAt</span>: <span class="hljs-keyword">new</span> <span class="hljs-built_in">Date</span>().toISOString(),
        <span class="hljs-attr">description</span>: <span class="hljs-string">`Document collection for user <span class="hljs-subst">${userId}</span>`</span>
      }
    });
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`Created new ChromaDB collection: <span class="hljs-subst">${collectionName}</span>`</span>);
    <span class="hljs-keyword">return</span> collection;
  }
}
</div></code></pre>
<h3 id="2-document-storage-isolation"><strong>2. Document Storage Isolation</strong></h3>
<h4 id="file-system-structure"><strong>File System Structure:</strong></h4>
<pre class="hljs"><code><div>DATA/
├── documents/
│   ├── user_a_id/
│   │   ├── document1.pdf
│   │   └── document2.pdf
│   ├── user_b_id/
│   │   ├── document3.pdf
│   │   └── document4.pdf
│   └── user_c_id/
│       └── document5.pdf
└── embeddings/
    ├── user_a_id/
    ├── user_b_id/
    └── user_c_id/
</div></code></pre>
<h4 id="document-upload-with-user-context"><strong>Document Upload with User Context:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// src/routes/documents.js</span>
router.post(<span class="hljs-string">'/upload'</span>, authenticateToken, upload.single(<span class="hljs-string">'file'</span>), <span class="hljs-keyword">async</span> (req, res) =&gt; {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">const</span> { sessionId } = req.body;
    <span class="hljs-keyword">const</span> userId = req.user.id; <span class="hljs-comment">// From authentication middleware</span>
    
    <span class="hljs-comment">// Create user-specific directory</span>
    <span class="hljs-keyword">const</span> userDir = path.join(documentsDir, userId);
    <span class="hljs-keyword">await</span> fsExtra.ensureDir(userDir);
    
    <span class="hljs-comment">// Store document with user context</span>
    <span class="hljs-keyword">const</span> <span class="hljs-built_in">document</span> = <span class="hljs-keyword">await</span> documentService.createDocument({
      <span class="hljs-attr">original_name</span>: file.originalname,
      <span class="hljs-attr">file_path</span>: filePath,
      <span class="hljs-attr">file_type</span>: file.mimetype,
      <span class="hljs-attr">file_size</span>: file.size,
      <span class="hljs-attr">user_id</span>: userId,        <span class="hljs-comment">// 🔑 User isolation key</span>
      <span class="hljs-attr">session_id</span>: sessionId || <span class="hljs-literal">null</span>,
      <span class="hljs-attr">status</span>: <span class="hljs-string">'uploaded'</span>
    });
    
    <span class="hljs-comment">// Queue for processing with user context</span>
    <span class="hljs-keyword">await</span> documentQueueService.addDocumentToQueue(<span class="hljs-built_in">document</span>.id, {
      <span class="hljs-attr">userId</span>: userId,         <span class="hljs-comment">// 🔑 Passed to workers</span>
      <span class="hljs-attr">sessionId</span>: sessionId,
      <span class="hljs-attr">priority</span>: <span class="hljs-number">0</span>
    });
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-comment">// Error handling</span>
  }
});
</div></code></pre>
<h3 id="3-embedding-storage-with-user-metadata"><strong>3. Embedding Storage with User Metadata</strong></h3>
<h4 id="chromadb-storage-with-user-context"><strong>ChromaDB Storage with User Context:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// src/services/vectorStoreService.js</span>
<span class="hljs-keyword">async</span> addDocumentChunks(documentId, chunks, embeddings, metadata = {}) {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">const</span> userId = metadata.userId;
    <span class="hljs-keyword">if</span> (!userId) {
      <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">'userId is required in metadata for user isolation'</span>);
    }

    <span class="hljs-comment">// Get user-specific collection</span>
    <span class="hljs-keyword">const</span> collection = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.getUserCollection(userId);
    
    <span class="hljs-comment">// Prepare user-specific metadata</span>
    <span class="hljs-keyword">const</span> ids = chunks.map(<span class="hljs-function">(<span class="hljs-params">_, index</span>) =&gt;</span> <span class="hljs-string">`<span class="hljs-subst">${documentId}</span>_chunk_<span class="hljs-subst">${index}</span>`</span>);
    <span class="hljs-keyword">const</span> documents = chunks;
    <span class="hljs-keyword">const</span> metadatas = chunks.map(<span class="hljs-function">(<span class="hljs-params">chunk, index</span>) =&gt;</span> ({
      <span class="hljs-attr">documentId</span>: documentId.toString(),
      <span class="hljs-attr">chunkIndex</span>: index.toString(),
      <span class="hljs-attr">sessionId</span>: metadata.sessionId ? metadata.sessionId.toString() : <span class="hljs-string">"no_session"</span>,
      <span class="hljs-attr">userId</span>: userId.toString(),           <span class="hljs-comment">// 🔑 User isolation in metadata</span>
      <span class="hljs-attr">timestamp</span>: <span class="hljs-keyword">new</span> <span class="hljs-built_in">Date</span>().toISOString(),
      <span class="hljs-attr">fileName</span>: metadata.fileName || <span class="hljs-string">"unknown"</span>,
      <span class="hljs-attr">fileType</span>: metadata.fileType || <span class="hljs-string">"unknown"</span>
    }));

    <span class="hljs-comment">// Store in user-specific collection</span>
    <span class="hljs-keyword">await</span> collection.add({
      <span class="hljs-attr">ids</span>: ids,
      <span class="hljs-attr">embeddings</span>: embeddings,
      <span class="hljs-attr">documents</span>: documents,
      <span class="hljs-attr">metadatas</span>: metadatas
    });
    
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`✅ Successfully stored <span class="hljs-subst">${embeddings.length}</span> vectors for document <span class="hljs-subst">${documentId}</span> in user collection`</span>);
    <span class="hljs-keyword">return</span> { <span class="hljs-attr">success</span>: <span class="hljs-literal">true</span> };
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">`❌ Failed to store document <span class="hljs-subst">${documentId}</span>:`</span>, error);
    <span class="hljs-keyword">return</span> { <span class="hljs-attr">success</span>: <span class="hljs-literal">false</span>, <span class="hljs-attr">error</span>: error.message };
  }
}
</div></code></pre>
<h3 id="4-search-isolation"><strong>4. Search Isolation</strong></h3>
<h4 id="user-specific-search"><strong>User-Specific Search:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// src/services/vectorStoreService.js</span>
<span class="hljs-keyword">async</span> search(queryEmbedding, options = {}) {
  <span class="hljs-keyword">const</span> { limit = <span class="hljs-number">10</span>, sessionId, userId } = options;
  
  <span class="hljs-keyword">if</span> (!userId) {
    <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">'userId is required for user-isolated search'</span>);
  }

  <span class="hljs-keyword">try</span> {
    <span class="hljs-comment">// Get user-specific collection</span>
    <span class="hljs-keyword">const</span> collection = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.getUserCollection(userId);
    
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`🔍 Searching ChromaDB for user <span class="hljs-subst">${userId}</span> with limit <span class="hljs-subst">${limit}</span>`</span>);
    
    <span class="hljs-comment">// Search only in user's collection</span>
    <span class="hljs-keyword">const</span> results = <span class="hljs-keyword">await</span> collection.query({
      <span class="hljs-attr">queryEmbeddings</span>: [queryEmbedding],
      <span class="hljs-attr">nResults</span>: limit,
      <span class="hljs-attr">where</span>: { <span class="hljs-attr">userId</span>: userId }  <span class="hljs-comment">// 🔑 Additional user filter</span>
    });

    <span class="hljs-comment">// Process and return results</span>
    <span class="hljs-keyword">if</span> (!results.documents || !results.documents[<span class="hljs-number">0</span>]) {
      <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`No results found for user <span class="hljs-subst">${userId}</span>`</span>);
      <span class="hljs-keyword">return</span> [];
    }

    <span class="hljs-keyword">return</span> results.documents[<span class="hljs-number">0</span>].map(<span class="hljs-function">(<span class="hljs-params">doc, index</span>) =&gt;</span> ({
      <span class="hljs-attr">content</span>: doc,
      <span class="hljs-attr">score</span>: results.distances[<span class="hljs-number">0</span>][index],
      <span class="hljs-attr">metadata</span>: results.metadatas[<span class="hljs-number">0</span>][index]
    }));
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">`Error searching for user <span class="hljs-subst">${userId}</span>:`</span>, error);
    <span class="hljs-keyword">return</span> [];
  }
}
</div></code></pre>
<h3 id="5-rag-toggle-isolation"><strong>5. RAG Toggle Isolation</strong></h3>
<h4 id="user-specific-rag-availability"><strong>User-Specific RAG Availability:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// src/services/ragService.js</span>
<span class="hljs-keyword">async</span> isRagAvailable(userId = <span class="hljs-literal">null</span>) {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.initializeServices();

    <span class="hljs-keyword">if</span> (!<span class="hljs-keyword">this</span>.vectorStoreService) {
      <span class="hljs-built_in">console</span>.log(<span class="hljs-string">'RAG: Vector store service not available'</span>);
      <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>;
    }

    <span class="hljs-comment">// Get user-specific stats</span>
    <span class="hljs-keyword">const</span> stats = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.vectorStoreService.getStats(userId);
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`RAG: Availability check - Vector store has <span class="hljs-subst">${stats.totalChunks}</span> chunks in <span class="hljs-subst">${stats.totalDocuments}</span> documents<span class="hljs-subst">${userId ? <span class="hljs-string">` for user <span class="hljs-subst">${userId}</span>`</span> : <span class="hljs-string">''</span>}</span>`</span>);

    <span class="hljs-keyword">const</span> isAvailable = stats.totalChunks &gt; <span class="hljs-number">0</span>;
    
    <span class="hljs-keyword">if</span> (isAvailable) {
      <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`RAG: Available for user <span class="hljs-subst">${userId}</span>`</span>);
    } <span class="hljs-keyword">else</span> {
      <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`RAG: Not available for user <span class="hljs-subst">${userId}</span> - no documents found`</span>);
    }

    <span class="hljs-keyword">return</span> isAvailable;
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">`RAG: Error checking availability for user <span class="hljs-subst">${userId}</span>:`</span>, error);
    <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>;
  }
}
</div></code></pre>
<h4 id="user-specific-stats-calculation"><strong>User-Specific Stats Calculation:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// src/services/vectorStoreService.js</span>
<span class="hljs-keyword">async</span> getStats(userId = <span class="hljs-literal">null</span>) {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">if</span> (!userId) {
      <span class="hljs-comment">// Return aggregate stats for admin</span>
      <span class="hljs-keyword">return</span> <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.getAggregateStats();
    }

    <span class="hljs-comment">// User-specific stats</span>
    <span class="hljs-keyword">if</span> (<span class="hljs-keyword">this</span>.useChromaDB &amp;&amp; <span class="hljs-keyword">this</span>.chromaClient) {
      <span class="hljs-keyword">try</span> {
        <span class="hljs-keyword">const</span> collection = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.getUserCollection(userId);
        <span class="hljs-keyword">const</span> count = <span class="hljs-keyword">await</span> collection.count();
        
        <span class="hljs-comment">// Get unique documents for this user</span>
        <span class="hljs-keyword">const</span> results = <span class="hljs-keyword">await</span> collection.get({
          <span class="hljs-attr">where</span>: { <span class="hljs-attr">userId</span>: userId }
        });
        
        <span class="hljs-keyword">const</span> uniqueDocuments = <span class="hljs-keyword">new</span> <span class="hljs-built_in">Set</span>();
        <span class="hljs-keyword">if</span> (results.metadatas) {
          results.metadatas.forEach(<span class="hljs-function"><span class="hljs-params">metadata</span> =&gt;</span> {
            <span class="hljs-keyword">if</span> (metadata.documentId) {
              uniqueDocuments.add(metadata.documentId);
            }
          });
        }
        
        <span class="hljs-keyword">return</span> {
          <span class="hljs-attr">totalChunks</span>: count,
          <span class="hljs-attr">totalDocuments</span>: uniqueDocuments.size,
          <span class="hljs-attr">userId</span>: userId
        };
      } <span class="hljs-keyword">catch</span> (error) {
        <span class="hljs-built_in">console</span>.error(<span class="hljs-string">`Error getting user stats for <span class="hljs-subst">${userId}</span>:`</span>, error);
        <span class="hljs-keyword">return</span> { <span class="hljs-attr">totalChunks</span>: <span class="hljs-number">0</span>, <span class="hljs-attr">totalDocuments</span>: <span class="hljs-number">0</span>, <span class="hljs-attr">userId</span>: userId };
      }
    }
    
    <span class="hljs-keyword">return</span> { <span class="hljs-attr">totalChunks</span>: <span class="hljs-number">0</span>, <span class="hljs-attr">totalDocuments</span>: <span class="hljs-number">0</span>, <span class="hljs-attr">userId</span>: userId };
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">'Error getting vector store stats:'</span>, error);
    <span class="hljs-keyword">return</span> { <span class="hljs-attr">totalChunks</span>: <span class="hljs-number">0</span>, <span class="hljs-attr">totalDocuments</span>: <span class="hljs-number">0</span>, <span class="hljs-attr">userId</span>: userId };
  }
}
</div></code></pre>
<h3 id="6-session-data-isolation"><strong>6. Session Data Isolation</strong></h3>
<h4 id="user-specific-session-deletion"><strong>User-Specific Session Deletion:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// src/services/vectorStoreService.js</span>
<span class="hljs-keyword">async</span> deleteSessionData(sessionId, userId) {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">if</span> (!userId) {
      <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">'userId is required for user isolation'</span>);
    }

    <span class="hljs-keyword">const</span> collection = <span class="hljs-keyword">await</span> <span class="hljs-keyword">this</span>.getUserCollection(userId);
    
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`🗑️ Deleting session data for sessionId: <span class="hljs-subst">${sessionId}</span>, userId: <span class="hljs-subst">${userId}</span>`</span>);
    
    <span class="hljs-comment">// Get all data for this user first</span>
    <span class="hljs-keyword">const</span> allResults = <span class="hljs-keyword">await</span> collection.get({
      <span class="hljs-attr">where</span>: { <span class="hljs-attr">userId</span>: userId }
    });
    
    <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`Found <span class="hljs-subst">${allResults.ids?.length || <span class="hljs-number">0</span>}</span> total chunks for user <span class="hljs-subst">${userId}</span>`</span>);
    
    <span class="hljs-comment">// Filter by sessionId</span>
    <span class="hljs-keyword">let</span> idsToDelete = [];
    <span class="hljs-keyword">if</span> (allResults.ids &amp;&amp; allResults.metadatas) {
      <span class="hljs-keyword">for</span> (<span class="hljs-keyword">let</span> i = <span class="hljs-number">0</span>; i &lt; allResults.ids.length; i++) {
        <span class="hljs-keyword">const</span> metadata = allResults.metadatas[i];
        <span class="hljs-keyword">if</span> (metadata.sessionId === sessionId || 
            (sessionId &amp;&amp; metadata.sessionId === <span class="hljs-string">"no_session"</span>)) {
          idsToDelete.push(allResults.ids[i]);
        }
      }
    }
    
    <span class="hljs-keyword">if</span> (idsToDelete.length &gt; <span class="hljs-number">0</span>) {
      <span class="hljs-keyword">await</span> collection.delete({ <span class="hljs-attr">ids</span>: idsToDelete });
      <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`✅ Deleted <span class="hljs-subst">${idsToDelete.length}</span> chunks for session <span class="hljs-subst">${sessionId}</span> (user <span class="hljs-subst">${userId}</span>)`</span>);
    } <span class="hljs-keyword">else</span> {
      <span class="hljs-built_in">console</span>.log(<span class="hljs-string">`No chunks found for session <span class="hljs-subst">${sessionId}</span> (user <span class="hljs-subst">${userId}</span>)`</span>);
    }
    
    <span class="hljs-keyword">return</span> { <span class="hljs-attr">success</span>: <span class="hljs-literal">true</span>, <span class="hljs-attr">deletedCount</span>: idsToDelete.length };
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">`❌ Error deleting session data:`</span>, error);
    <span class="hljs-keyword">return</span> { <span class="hljs-attr">success</span>: <span class="hljs-literal">false</span>, <span class="hljs-attr">error</span>: error.message };
  }
}
</div></code></pre>
<hr>
<h2 id="%F0%9F%94%84-api-integration-with-user-context">🔄 <strong>API Integration with User Context</strong></h2>
<h3 id="frontend-rag-toggle-check"><strong>Frontend RAG Toggle Check:</strong></h3>
<pre class="hljs"><code><div><span class="hljs-comment">// client/src/services/ragChatService.ts</span>
<span class="hljs-keyword">async</span> checkRagAvailability(): <span class="hljs-built_in">Promise</span>&lt;<span class="hljs-built_in">boolean</span>&gt; {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">const</span> response = <span class="hljs-keyword">await</span> fetch(<span class="hljs-string">'/api/ollama/rag-available'</span>, {
      method: <span class="hljs-string">'GET'</span>,
      credentials: <span class="hljs-string">'include'</span>, <span class="hljs-comment">// 🔑 Includes user session</span>
      headers: {
        <span class="hljs-string">'Content-Type'</span>: <span class="hljs-string">'application/json'</span>,
      },
    });
    
    <span class="hljs-keyword">if</span> (!response.ok) {
      <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">`HTTP error! status: <span class="hljs-subst">${response.status}</span>`</span>);
    }
    
    <span class="hljs-keyword">const</span> data = <span class="hljs-keyword">await</span> response.json();
    <span class="hljs-keyword">return</span> data.available || <span class="hljs-literal">false</span>;
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">'Error checking RAG availability:'</span>, error);
    <span class="hljs-keyword">return</span> <span class="hljs-literal">false</span>;
  }
}
</div></code></pre>
<h3 id="backend-rag-availability-endpoint"><strong>Backend RAG Availability Endpoint:</strong></h3>
<pre class="hljs"><code><div><span class="hljs-comment">// src/routes/ollama.js</span>
router.get(<span class="hljs-string">'/rag-available'</span>, authenticateToken, <span class="hljs-keyword">async</span> (req, res) =&gt; {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">const</span> userId = req.user.id; <span class="hljs-comment">// 🔑 From authentication middleware</span>
    
    <span class="hljs-comment">// Check RAG availability for this specific user</span>
    <span class="hljs-keyword">const</span> isAvailable = <span class="hljs-keyword">await</span> ragService.isRagAvailable(userId);
    
    res.json({ 
      <span class="hljs-attr">available</span>: isAvailable,
      <span class="hljs-attr">userId</span>: userId,
      <span class="hljs-attr">timestamp</span>: <span class="hljs-keyword">new</span> <span class="hljs-built_in">Date</span>().toISOString()
    });
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">'Error checking RAG availability:'</span>, error);
    res.status(<span class="hljs-number">500</span>).json({ 
      <span class="hljs-attr">available</span>: <span class="hljs-literal">false</span>, 
      <span class="hljs-attr">error</span>: <span class="hljs-string">'Failed to check RAG availability'</span> 
    });
  }
});
</div></code></pre>
<h3 id="rag-chat-with-user-context"><strong>RAG Chat with User Context:</strong></h3>
<pre class="hljs"><code><div><span class="hljs-comment">// src/routes/ollama.js</span>
router.post(<span class="hljs-string">'/rag-chat'</span>, authenticateToken, <span class="hljs-keyword">async</span> (req, res) =&gt; {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">const</span> { message, model, sessionId } = req.body;
    <span class="hljs-keyword">const</span> userId = req.user.id; <span class="hljs-comment">// 🔑 From authentication middleware</span>
    
    <span class="hljs-comment">// Process RAG chat with user context</span>
    <span class="hljs-keyword">const</span> result = <span class="hljs-keyword">await</span> ragService.processRagChat(message, model, {
      <span class="hljs-attr">sessionId</span>: sessionId,
      <span class="hljs-attr">userId</span>: userId  <span class="hljs-comment">// 🔑 User isolation in RAG processing</span>
    });
    
    <span class="hljs-keyword">if</span> (result.success) {
      res.json({
        <span class="hljs-attr">response</span>: result.response,
        <span class="hljs-attr">sources</span>: result.sources,
        <span class="hljs-attr">context</span>: result.context
      });
    } <span class="hljs-keyword">else</span> {
      res.status(<span class="hljs-number">500</span>).json({ <span class="hljs-attr">error</span>: result.error });
    }
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-built_in">console</span>.error(<span class="hljs-string">'Error in RAG chat:'</span>, error);
    res.status(<span class="hljs-number">500</span>).json({ <span class="hljs-attr">error</span>: <span class="hljs-string">'Failed to process RAG chat'</span> });
  }
});
</div></code></pre>
<hr>
<h2 id="%F0%9F%94%90-security-considerations">🔐 <strong>Security Considerations</strong></h2>
<h3 id="1-authentication-middleware"><strong>1. Authentication Middleware:</strong></h3>
<pre class="hljs"><code><div><span class="hljs-comment">// src/middleware/auth.js</span>
<span class="hljs-keyword">const</span> authenticateToken = <span class="hljs-function">(<span class="hljs-params">req, res, next</span>) =&gt;</span> {
  <span class="hljs-comment">// Extract user from session/token</span>
  <span class="hljs-keyword">if</span> (req.session &amp;&amp; req.session.userId) {
    req.user = { <span class="hljs-attr">id</span>: req.session.userId };
    next();
  } <span class="hljs-keyword">else</span> {
    res.status(<span class="hljs-number">401</span>).json({ <span class="hljs-attr">error</span>: <span class="hljs-string">'Unauthorized'</span> });
  }
};
</div></code></pre>
<h3 id="2-user-id-validation"><strong>2. User ID Validation:</strong></h3>
<pre class="hljs"><code><div><span class="hljs-comment">// Validate UUID format</span>
<span class="hljs-keyword">const</span> isValidUserId = <span class="hljs-function">(<span class="hljs-params">userId</span>) =&gt;</span> {
  <span class="hljs-keyword">const</span> uuidRegex = <span class="hljs-regexp">/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i</span>;
  <span class="hljs-keyword">return</span> uuidRegex.test(userId);
};

<span class="hljs-comment">// Sanitize collection names</span>
<span class="hljs-keyword">const</span> sanitizeCollectionName = <span class="hljs-function">(<span class="hljs-params">userId</span>) =&gt;</span> {
  <span class="hljs-keyword">if</span> (!isValidUserId(userId)) {
    <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">'Invalid user ID format'</span>);
  }
  <span class="hljs-keyword">return</span> <span class="hljs-string">`user_<span class="hljs-subst">${userId.replace(<span class="hljs-regexp">/-/g</span>, <span class="hljs-string">'_'</span>)}</span>_docs`</span>;
};
</div></code></pre>
<h3 id="3-cross-user-access-prevention"><strong>3. Cross-User Access Prevention:</strong></h3>
<pre class="hljs"><code><div><span class="hljs-comment">// Always verify user ownership</span>
<span class="hljs-keyword">const</span> verifyUserAccess = <span class="hljs-keyword">async</span> (documentId, userId) =&gt; {
  <span class="hljs-keyword">const</span> <span class="hljs-built_in">document</span> = <span class="hljs-keyword">await</span> documentService.getDocument(documentId);
  <span class="hljs-keyword">if</span> (<span class="hljs-built_in">document</span>.user_id !== userId) {
    <span class="hljs-keyword">throw</span> <span class="hljs-keyword">new</span> <span class="hljs-built_in">Error</span>(<span class="hljs-string">'Access denied: Document belongs to different user'</span>);
  }
  <span class="hljs-keyword">return</span> <span class="hljs-built_in">document</span>;
};
</div></code></pre>
<hr>
<h2 id="%F0%9F%93%8A-testing-user-isolation">📊 <strong>Testing User Isolation</strong></h2>
<h3 id="test-scenarios"><strong>Test Scenarios:</strong></h3>
<h4 id="1-document-upload-isolation"><strong>1. Document Upload Isolation:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// Test: User A uploads document</span>
<span class="hljs-comment">// Expected: Document only visible to User A</span>
<span class="hljs-comment">// Verification: User B cannot see User A's document</span>
</div></code></pre>
<h4 id="2-rag-toggle-independence"><strong>2. RAG Toggle Independence:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// Test: User A has documents, User B has none</span>
<span class="hljs-comment">// Expected: User A sees RAG enabled, User B sees RAG disabled</span>
<span class="hljs-comment">// Verification: Each user's toggle reflects their own data</span>
</div></code></pre>
<h4 id="3-search-isolation"><strong>3. Search Isolation:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// Test: User A searches for content in User B's documents</span>
<span class="hljs-comment">// Expected: No results returned</span>
<span class="hljs-comment">// Verification: Search only returns User A's documents</span>
</div></code></pre>
<h4 id="4-session-deletion-isolation"><strong>4. Session Deletion Isolation:</strong></h4>
<pre class="hljs"><code><div><span class="hljs-comment">// Test: User A deletes their session</span>
<span class="hljs-comment">// Expected: Only User A's session data is deleted</span>
<span class="hljs-comment">// Verification: User B's data remains intact</span>
</div></code></pre>
<hr>
<h2 id="%F0%9F%8E%AF-benefits-achieved">🎯 <strong>Benefits Achieved</strong></h2>
<h3 id="1-complete-data-separation"><strong>1. Complete Data Separation:</strong></h3>
<ul>
<li>✅ Each user has their own ChromaDB collection</li>
<li>✅ File system isolation by user directory</li>
<li>✅ Metadata includes user identification</li>
</ul>
<h3 id="2-independent-rag-functionality"><strong>2. Independent RAG Functionality:</strong></h3>
<ul>
<li>✅ RAG toggle reflects individual user's document status</li>
<li>✅ Search results limited to user's own documents</li>
<li>✅ Chat responses based only on user's uploaded content</li>
</ul>
<h3 id="3-scalable-architecture"><strong>3. Scalable Architecture:</strong></h3>
<ul>
<li>✅ Supports unlimited users</li>
<li>✅ No performance impact from other users' data</li>
<li>✅ Easy to add new isolation features</li>
</ul>
<h3 id="4-security--privacy"><strong>4. Security &amp; Privacy:</strong></h3>
<ul>
<li>✅ No cross-user data leakage</li>
<li>✅ User authentication enforced at all levels</li>
<li>✅ Session-based access control</li>
</ul>
<hr>
<h2 id="%F0%9F%94%A7-key-implementation-files">🔧 <strong>Key Implementation Files</strong></h2>
<h3 id="modified-files-for-user-isolation"><strong>Modified Files for User Isolation:</strong></h3>
<ol>
<li>
<p><strong><code>src/services/vectorStoreService.js</code></strong></p>
<ul>
<li>User-specific collection management</li>
<li>Isolated search functionality</li>
<li>User-specific stats calculation</li>
</ul>
</li>
<li>
<p><strong><code>src/services/ragService.js</code></strong></p>
<ul>
<li>User context in RAG availability checks</li>
<li>User-specific document retrieval</li>
</ul>
</li>
<li>
<p><strong><code>src/services/documentProcessor.js</code></strong></p>
<ul>
<li>User metadata in embedding storage</li>
<li>User context preservation through processing</li>
</ul>
</li>
<li>
<p><strong><code>src/routes/documents.js</code></strong></p>
<ul>
<li>User authentication integration</li>
<li>User-specific file storage</li>
</ul>
</li>
<li>
<p><strong><code>src/routes/ollama.js</code></strong></p>
<ul>
<li>User context in all RAG endpoints</li>
<li>User-specific RAG availability</li>
</ul>
</li>
<li>
<p><strong><code>client/src/services/ragChatService.ts</code></strong></p>
<ul>
<li>Session-based user identification</li>
<li>User-specific API calls</li>
</ul>
</li>
</ol>
<hr>
<h2 id="%F0%9F%93%88-performance-impact">📈 <strong>Performance Impact</strong></h2>
<h3 id="minimal-overhead"><strong>Minimal Overhead:</strong></h3>
<ul>
<li><strong>Collection Creation</strong>: One-time cost per user</li>
<li><strong>Search Performance</strong>: Improved (smaller collections)</li>
<li><strong>Storage Efficiency</strong>: Better organization</li>
<li><strong>Memory Usage</strong>: Unchanged</li>
</ul>
<h3 id="scalability-benefits"><strong>Scalability Benefits:</strong></h3>
<ul>
<li><strong>Linear Scaling</strong>: Performance scales with individual user data size</li>
<li><strong>Isolation Benefits</strong>: One user's large dataset doesn't affect others</li>
<li><strong>Maintenance</strong>: Easier to manage user-specific issues</li>
</ul>
<p>This user isolation implementation ensures complete data separation while maintaining high performance and scalability for multi-user RAG applications.</p>

</body>

</html>