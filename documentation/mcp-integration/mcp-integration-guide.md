# MCP Integration Guide

This document explains how the MCP (Model Context Protocol) server works and how clients can integrate with it, focusing on session management, client connections, and tool invocation.

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Session Management](#session-management)
4. [Client Connection Flow](#client-connection-flow)
5. [Tool Invocation](#tool-invocation)
6. [ID Assignment](#id-assignment)
7. [Integration with AI Applications](#integration-with-ai-applications)
8. [Example Client Implementation](#example-client-implementation)
9. [Troubleshooting](#troubleshooting)

## Introduction

The MCP (Model Context Protocol) server is a standardized interface that allows AI applications to interact with system resources through a set of predefined tools. It provides a secure and consistent way for AI models to:

- Execute shell commands
- Manage files and directories
- Run Python scripts
- Search for patterns in files
- Perform other system operations

The server is built on Node.js and Express, using the Model Context Protocol SDK (`@modelcontextprotocol/sdk`) to standardize communication between AI clients and system resources.

## Architecture Overview

The MCP server consists of several key components:

1. **HTTP API Server**: Handles client connections and tool invocations
2. **Server-Sent Events (SSE) Endpoint**: Maintains persistent connections with clients
3. **Tool Registry**: Manages the available tools and their schemas
4. **Session Manager**: Tracks client sessions and their state
5. **Tools Discovery API**: Allows clients to discover available tools

The server exposes several endpoints:
- `/sse` - For establishing persistent connections
- `/messages` - For invoking tools
- `/tools` - For discovering available tools
- `/info` - For retrieving server information

## Session Management

The MCP server uses a session-based approach to manage client connections:

1. Each client establishes a connection through the SSE endpoint
2. The server assigns a unique client ID to each connection
3. The client uses this ID for all subsequent tool invocations
4. The server maintains a session map that tracks all active connections
5. Sessions are automatically cleaned up when clients disconnect

```javascript
// Session structure in the server
interface ClientSession {
  res: Response;           // The SSE response object
  messageQueue: any[];     // Queue for pending messages
  lastPing: number;        // Timestamp of last ping
}

// Sessions are stored in a Map
const sessions = new Map<string, ClientSession>();
```

## Client Connection Flow

The connection flow between a client and the MCP server follows these steps:

1. **Establish SSE Connection**:
   - Client makes a GET request to `/sse`
   - Server generates a unique client ID (timestamp-based)
   - Server sets up SSE headers and keeps the connection open
   - Server sends the client ID in the initial message

2. **Maintain Connection**:
   - Server sends periodic pings to keep the connection alive
   - Client listens for messages on the SSE connection
   - Server tracks the last ping time for each client

3. **Invoke Tools**:
   - Client sends POST requests to `/messages` with the client ID
   - Server validates the client ID against active sessions
   - Server executes the requested tool
   - Server sends the result via both HTTP response and SSE

4. **Handle Disconnection**:
   - When client closes the connection, the server detects it
   - Server removes the client session from the sessions map
   - Server cleans up any resources associated with the session

## Tool Invocation

To invoke a tool on the MCP server, clients send a POST request to the `/messages` endpoint with the following structure:

```json
{
  "id": "message-id",
  "type": "invoke_tool",
  "content": {
    "name": "toolName",
    "parameters": {
      "param1": "value1",
      "param2": "value2"
    }
  },
  "clientId": "client-id-from-sse-connection"
}
```

The server processes this request by:
1. Validating the client ID against active sessions
2. Looking up the requested tool
3. Validating the parameters against the tool's schema
4. Executing the tool with the provided parameters
5. Returning the result in both the HTTP response and via SSE

## ID Assignment

The MCP server uses a simple but effective approach to ID assignment:

1. **Client IDs**: Generated using `Date.now().toString()` when a client connects to the SSE endpoint
   - This creates a timestamp-based unique identifier
   - The timestamp ensures uniqueness across connections
   - Example: `"1683721045873"`

2. **Message IDs**: Generated by clients, typically using a similar timestamp approach
   - Clients should ensure message IDs are unique within their session
   - Example: `"msg-1683721046123"`

This approach ensures:
- IDs are unique across different clients
- No central coordination is needed for ID generation
- IDs can be easily traced for debugging purposes
- The system scales well with multiple concurrent clients

## Integration with AI Applications

To integrate an AI application with the MCP server:

1. **Establish Connection**:
   - Create an SSE connection to the MCP server
   - Store the client ID received from the server

2. **Discover Available Tools**:
   - Query the `/tools` endpoint to get information about available tools
   - Parse the tool schemas to understand parameter requirements

3. **Invoke Tools**:
   - Use the client ID in all tool invocation requests
   - Format parameters according to the tool's schema
   - Handle both success and error responses

4. **Process Results**:
   - Parse the tool results returned by the server
   - Update the AI application's state based on the results
   - Display relevant information to the user

## Example Client Implementation

Here's a simplified example of a client implementation in JavaScript:

```javascript
class McpClient {
  constructor(serverUrl = 'http://localhost:8080') {
    this.serverUrl = serverUrl;
    this.clientId = null;
    this.eventSource = null;
  }

  // Connect to the MCP server
  connect() {
    return new Promise((resolve, reject) => {
      this.eventSource = new EventSource(`${this.serverUrl}/sse`);
      
      this.eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        if (data.type === 'connected') {
          this.clientId = data.clientId;
          resolve(this.clientId);
        } else if (data.type === 'tool_result') {
          // Handle tool results
          console.log('Tool result:', data.content);
        }
      };
      
      this.eventSource.onerror = (error) => {
        reject(error);
      };
    });
  }
  
  // Invoke a tool on the MCP server
  async invokeTool(toolName, parameters) {
    if (!this.clientId) {
      throw new Error('Not connected to MCP server');
    }
    
    const response = await fetch(`${this.serverUrl}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        id: `msg-${Date.now()}`,
        type: 'invoke_tool',
        content: {
          name: toolName,
          parameters: parameters
        },
        clientId: this.clientId
      })
    });
    
    return await response.json();
  }

  // Disconnect from the MCP server
  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      this.clientId = null;
    }
  }
}
```

## Troubleshooting

### Common Issues

1. **"No active SSE connection" error**:
   - The client ID may be invalid or expired
   - The server may have restarted, invalidating all sessions
   - Solution: Reconnect to get a new client ID

2. **Connection timeouts**:
   - The server may be behind a proxy that times out long connections
   - Solution: Configure the proxy to allow long-lived connections or implement reconnection logic

3. **Tool invocation failures**:
   - Check that the tool name is correct
   - Verify that all required parameters are provided
   - Ensure parameter types match the tool's schema
   - Solution: Query the `/tools` endpoint to get accurate tool information

4. **Performance issues**:
   - Avoid making too many concurrent tool invocations
   - Consider batching operations when possible
   - For large file operations, use streaming tools when available
