# Example AI Rules for Testing

Below are several example AI rules that you can copy and paste into the AI Rules settings to test the functionality. These examples range from simple language preferences to more complex coding and formatting instructions.

## Basic Language and Style Preferences

```
Always respond in a concise manner, avoiding unnecessary explanations unless I specifically ask for more details. Use simple language and avoid jargon when possible.
```

```
I prefer responses in British English. Use British spelling (e.g., "colour" instead of "color") and British expressions where appropriate.
```

```
Please format all code examples with proper syntax highlighting and indentation. Always include comments explaining complex parts of the code.
```

## Programming Language Preferences

```
When providing code examples, prefer Python over other languages unless I specifically ask for another language. Use Python 3.9+ syntax and follow PEP 8 style guidelines.
```

```
I work primarily with TypeScript and React. When showing code examples:
1. Always use TypeScript instead of JavaScript
2. Use functional components with hooks instead of class components
3. Use ES6+ syntax (arrow functions, destructuring, etc.)
4. Follow the React team's recommended patterns
```

```
When explaining database concepts, use PostgreSQL syntax for SQL examples. I prefer using ORMs like Sequelize or TypeORM in application code rather than raw SQL.
```

## Domain-Specific Instructions

```
I'm a data scientist working primarily with pandas, scikit-learn, and TensorFlow. When explaining data analysis concepts:
1. Include pandas DataFrame examples where relevant
2. Show both the code and sample output when possible
3. Explain machine learning concepts in terms of practical implementation
```

```
I'm a DevOps engineer working with Kubernetes and AWS. When discussing infrastructure:
1. Provide examples using kubectl commands or Kubernetes YAML
2. Show AWS CLI commands or CloudFormation/Terraform examples where appropriate
3. Focus on security best practices and scalability
```

## Combined Preferences

```
General preferences:
- Respond in concise, clear language
- Use bullet points and numbered lists for complex explanations
- Include diagrams or ASCII art for visual concepts when helpful

Code preferences:
- Use TypeScript for frontend examples
- Use Python for backend or data processing examples
- Always include error handling in code examples
- Comment code thoroughly

Technical focus:
- I work primarily with React, Node.js, and PostgreSQL
- I'm interested in performance optimization and security best practices
- I prefer modern, maintainable approaches over quick hacks
```

## MCP Tool Usage Preferences

```
When suggesting MCP tools:
1. Always explain what the tool does before suggesting it
2. Show the exact parameters needed for the tool
3. Explain potential risks or side effects of using the tool
4. Provide alternatives when available
```

```
For file operations using MCP tools:
- Always confirm before suggesting operations that modify or delete files
- Use relative paths when possible
- Show how to verify the results after the operation
- Explain any error messages that might occur
```

## Conversation Style Preferences

```
I prefer a collaborative, educational conversation style. Explain concepts as if you're teaching, not just providing information. Use analogies and examples to illustrate complex ideas.
```

```
I learn best through practical examples. When explaining concepts:
1. Start with a simple, concrete example
2. Build up to more complex cases
3. Show common pitfalls and how to avoid them
4. Provide resources for further learning
```

Feel free to combine elements from different examples or modify them to suit your specific needs. These rules will help the AI understand your preferences and provide more tailored responses.
