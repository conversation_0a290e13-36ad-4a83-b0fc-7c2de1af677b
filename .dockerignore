# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Docker
.dockerignore
Dockerfile
docker-compose.yml

# Git
.git
.gitignore

# IDE
.idea
.vscode
*.swp
*.swo

# Logs
logs/*
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.*

# Temporary files
tmp/
temp/

# Keep necessary directories
!logs/.gitkeep 