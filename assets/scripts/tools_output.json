{"count":16,"tools":[{"name":"runShellCommand","description":"Run a terminal command in the system shell","parameters":{"command":{"type":"ZodString","description":"The shell command to execute","required":true}},"examples":["runShellCommand({\n  \"command\": \"\\\"ls -la\\\"\"\n})"]},{"name":"runPythonFile","description":"Execute a Python file and return output","parameters":{"filePath":{"type":"ZodString","description":"Path to the Python file to execute","required":true},"args":{"type":"ZodOptional","description":"Optional arguments to pass to the Python script","required":true}},"examples":["runPythonFile({\n  \"filePath\": \"\\\"newfile.txt\\\"\",\n  \"args\": \"\\\"\\\"\"\n})"]},{"name":"readDirectory","description":"List files and folders in a directory","parameters":{"dirPath":{"type":"ZodOptional","description":"Path to the directory to read (default: workspace root)","required":true}},"examples":["readDirectory({\n  \"dirPath\": \"\\\"directory\\\"\"\n})"]},{"name":"copyFile","description":"Copy a file from one location to another","parameters":{"sourcePath":{"type":"ZodString","description":"Path to the source file","required":true},"destinationPath":{"type":"ZodString","description":"Path to the destination file","required":true}},"examples":["copyFile({\n  \"sourcePath\": \"\\\"file.txt\\\"\",\n  \"destinationPath\": \"\\\"newfile.txt\\\"\"\n})"]},{"name":"createFile","description":"Create a new file with specified contents","parameters":{"filePath":{"type":"ZodString","description":"Path to the file to create","required":true},"content":{"type":"ZodString","description":"Content to write to the file","required":true}},"examples":["createFile({\n  \"filePath\": \"\\\"newfile.txt\\\"\",\n  \"content\": \"\\\"Sample content\\\"\"\n})"]},{"name":"readFile","description":"Read the content of a file","parameters":{"filePath":{"type":"ZodString","description":"Path to the file to read","required":true},"encoding":{"type":"ZodOptional","description":"File encoding (default: utf8)","required":true},"startLine":{"type":"ZodOptional","description":"Start line (0-based, inclusive)","required":true},"endLine":{"type":"ZodOptional","description":"End line (0-based, inclusive)","required":true}},"examples":["readFile({\n  \"filePath\": \"\\\"newfile.txt\\\"\",\n  \"encoding\": \"\\\"\\\"\",\n  \"startLine\": \"\\\"\\\"\",\n  \"endLine\": \"\\\"\\\"\"\n})"]},{"name":"editFile","description":"Edit the content of an existing file","parameters":{"filePath":{"type":"ZodString","description":"Path to the file to edit","required":true},"operation":{"type":"ZodEnum","description":"Edit operation to perform","required":true,"enum":["append","prepend","replace","insert"]},"content":{"type":"ZodString","description":"Content to add or replace with","required":true},"lineNumber":{"type":"ZodOptional","description":"Line number for insert operation (0-based)","required":true},"startLine":{"type":"ZodOptional","description":"Start line for replace operation (0-based, inclusive)","required":true},"endLine":{"type":"ZodOptional","description":"End line for replace operation (0-based, inclusive)","required":true},"encoding":{"type":"ZodOptional","description":"File encoding (default: utf8)","required":true}},"examples":["editFile({\n  \"filePath\": \"\\\"newfile.txt\\\"\",\n  \"operation\": \"\\\"\\\"\",\n  \"content\": \"\\\"Sample content\\\"\",\n  \"lineNumber\": \"\\\"\\\"\",\n  \"startLine\": \"\\\"\\\"\",\n  \"endLine\": \"\\\"\\\"\",\n  \"encoding\": \"\\\"\\\"\"\n})"]},{"name":"deleteFile","description":"Delete a file from the filesystem","parameters":{"filePath":{"type":"ZodString","description":"Path to the file to delete","required":true}},"examples":["deleteFile({\n  \"filePath\": \"\\\"newfile.txt\\\"\"\n})"]},{"name":"moveFile","description":"Move a file from