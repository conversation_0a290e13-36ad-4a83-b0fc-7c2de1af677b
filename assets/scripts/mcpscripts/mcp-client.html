<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        .connected {
            background-color: green;
        }
        .disconnected {
            background-color: red;
        }
        .connecting {
            background-color: orange;
        }
        .tools-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        .tool-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: white;
        }
        .tool-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .tool-description {
            color: #555;
            margin-bottom: 10px;
        }
        .tool-params {
            font-size: 0.9em;
            color: #777;
        }
        .command-form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            white-space: pre-wrap;
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MCP Client</h1>
        
        <div class="card">
            <h2>Server Status</h2>
            <div class="status">
                <div id="status-indicator" class="status-indicator disconnected"></div>
                <div id="status-text">Disconnected</div>
            </div>
            <div id="server-info"></div>
            <button id="connect-btn">Connect to MCP Server</button>
        </div>
        
        <div class="card">
            <h2>Available Tools</h2>
            <div id="tools-list" class="tools-list">
                <p>Connect to the server to see available tools</p>
            </div>
        </div>
        
        <div class="card">
            <h2>Execute Command</h2>
            <div class="command-form">
                <div class="form-group">
                    <label for="tool-select">Select Tool:</label>
                    <select id="tool-select">
                        <option value="">-- Select a tool --</option>
                    </select>
                </div>
                <div id="params-container"></div>
                <button id="execute-btn" disabled>Execute Command</button>
            </div>
        </div>
        
        <div class="card">
            <h2>Result</h2>
            <div id="result" class="result">No results yet</div>
        </div>
    </div>

    <script>
        // Global variables
        let eventSource = null;
        let clientId = null;
        let serverInfo = null;
        let availableTools = [];
        
        // DOM elements
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const serverInfoEl = document.getElementById('server-info');
        const connectBtn = document.getElementById('connect-btn');
        const toolsList = document.getElementById('tools-list');
        const toolSelect = document.getElementById('tool-select');
        const paramsContainer = document.getElementById('params-container');
        const executeBtn = document.getElementById('execute-btn');
        const resultEl = document.getElementById('result');
        
        // Server configuration
        const MCP_HOST = '************';
        const MCP_PORT = '8080';
        
        // Connect to the MCP server
        connectBtn.addEventListener('click', connectToServer);
        
        // Handle tool selection
        toolSelect.addEventListener('change', function() {
            const selectedTool = availableTools.find(tool => tool.name === this.value);
            if (selectedTool) {
                generateParamsForm(selectedTool);
                executeBtn.disabled = false;
            } else {
                paramsContainer.innerHTML = '';
                executeBtn.disabled = true;
            }
        });
        
        // Execute command
        executeBtn.addEventListener('click', executeCommand);
        
        // Connect to the MCP server
        function connectToServer() {
            // Update status
            updateStatus('connecting', 'Connecting to MCP server...');
            
            // Fetch server info
            fetch(`http://${MCP_HOST}:${MCP_PORT}/info`)
                .then(response => response.json())
                .then(info => {
                    serverInfo = info;
                    serverInfoEl.innerHTML = `
                        <p><strong>Name:</strong> ${info.name}</p>
                        <p><strong>Version:</strong> ${info.version}</p>
                        <p><strong>Platform:</strong> ${info.platform}</p>
                        <p><strong>Workspace:</strong> ${info.workspace}</p>
                    `;
                    
                    // Establish SSE connection
                    connectSSE();
                    
                    // Fetch available tools
                    fetchTools();
                })
                .catch(error => {
                    console.error('Error fetching server info:', error);
                    updateStatus('disconnected', 'Failed to connect to MCP server');
                    resultEl.textContent = `Error: ${error.message}`;
                });
        }
        
        // Connect to SSE endpoint
        function connectSSE() {
            if (eventSource) {
                eventSource.close();
            }
            
            try {
                eventSource = new EventSource(`http://${MCP_HOST}:${MCP_PORT}/sse`);
                
                eventSource.onopen = function() {
                    console.log('SSE connection opened');
                };
                
                eventSource.onerror = function(error) {
                    console.error('SSE connection error:', error);
                    updateStatus('disconnected', 'SSE connection error');
                };
                
                eventSource.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('SSE message received:', data);
                        
                        if (data.type === 'connected' && data.clientId) {
                            clientId = data.clientId;
                            updateStatus('connected', `Connected to MCP server (Client ID: ${clientId})`);
                        }
                    } catch (error) {
                        console.error('Error parsing SSE event:', error);
                    }
                };
            } catch (error) {
                console.error('Error establishing SSE connection:', error);
                updateStatus('disconnected', 'Failed to establish SSE connection');
            }
        }
        
        // Fetch available tools
        function fetchTools() {
            fetch(`http://${MCP_HOST}:${MCP_PORT}/tools`)
                .then(response => response.json())
                .then(data => {
                    availableTools = data.tools || [];
                    
                    // Update tools list
                    toolsList.innerHTML = '';
                    toolSelect.innerHTML = '<option value="">-- Select a tool --</option>';
                    
                    availableTools.forEach(tool => {
                        // Add to tools list
                        const toolCard = document.createElement('div');
                        toolCard.className = 'tool-card';
                        toolCard.innerHTML = `
                            <div class="tool-name">${tool.name}</div>
                            <div class="tool-description">${tool.description}</div>
                            <div class="tool-params">Parameters: ${Object.keys(tool.parameters || {}).join(', ') || 'None'}</div>
                        `;
                        toolsList.appendChild(toolCard);
                        
                        // Add to tool select
                        const option = document.createElement('option');
                        option.value = tool.name;
                        option.textContent = tool.name;
                        toolSelect.appendChild(option);
                    });
                    
                    resultEl.textContent = `Successfully fetched ${availableTools.length} tools`;
                })
                .catch(error => {
                    console.error('Error fetching tools:', error);
                    resultEl.textContent = `Error fetching tools: ${error.message}`;
                });
        }
        
        // Generate parameters form for selected tool
        function generateParamsForm(tool) {
            paramsContainer.innerHTML = '';
            
            if (!tool.parameters || Object.keys(tool.parameters).length === 0) {
                paramsContainer.innerHTML = '<p>This tool has no parameters</p>';
                return;
            }
            
            for (const [paramName, paramInfo] of Object.entries(tool.parameters)) {
                const formGroup = document.createElement('div');
                formGroup.className = 'form-group';
                
                const label = document.createElement('label');
                label.setAttribute('for', `param-${paramName}`);
                label.textContent = `${paramName}${paramInfo.required ? ' *' : ''}:`;
                
                const input = document.createElement('input');
                input.setAttribute('id', `param-${paramName}`);
                input.setAttribute('name', paramName);
                input.setAttribute('placeholder', paramInfo.description || paramName);
                
                formGroup.appendChild(label);
                formGroup.appendChild(input);
                paramsContainer.appendChild(formGroup);
            }
        }
        
        // Execute command
        function executeCommand() {
            const toolName = toolSelect.value;
            if (!toolName) {
                resultEl.textContent = 'Please select a tool';
                return;
            }
            
            if (!clientId) {
                resultEl.textContent = 'Not connected to MCP server';
                return;
            }
            
            // Collect parameters
            const parameters = {};
            const paramInputs = paramsContainer.querySelectorAll('input');
            paramInputs.forEach(input => {
                parameters[input.name] = input.value;
            });
            
            // Prepare payload
            const payload = {
                clientId,
                tool: toolName,
                parameters
            };
            
            // Send command
            resultEl.textContent = 'Executing command...';
            
            fetch(`http://${MCP_HOST}:${MCP_PORT}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            })
                .then(response => response.json())
                .then(data => {
                    console.log('Command result:', data);
                    
                    if (data.error) {
                        resultEl.textContent = `Error: ${data.error.message || JSON.stringify(data.error)}`;
                    } else if (data.content) {
                        // Format the result
                        let resultText = '';
                        
                        if (Array.isArray(data.content)) {
                            data.content.forEach(item => {
                                if (item.type === 'text') {
                                    resultText += item.text + '\n';
                                } else {
                                    resultText += JSON.stringify(item, null, 2) + '\n';
                                }
                            });
                        } else {
                            resultText = JSON.stringify(data.content, null, 2);
                        }
                        
                        resultEl.textContent = resultText;
                    } else {
                        resultEl.textContent = JSON.stringify(data, null, 2);
                    }
                })
                .catch(error => {
                    console.error('Error executing command:', error);
                    resultEl.textContent = `Error executing command: ${error.message}`;
                });
        }
        
        // Update connection status
        function updateStatus(status, text) {
            statusIndicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }
    </script>
</body>
</html>
