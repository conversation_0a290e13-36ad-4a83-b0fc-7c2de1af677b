{"name": "platform-dashboard", "version": "1.0.0", "description": "A modern platform dashboard application for user management and monitoring", "main": "src/server.js", "scripts": {"start": "node src/server.js --config=./conf/config.ini", "build": "cd client && npm run build", "deploy": "npm run build && npm run start", "dev": "nodemon src/server.js --config=./conf/config.ini", "dev:client": "cd client && npm run build -- --watch", "dev:script": "node src/scripts/dev.js", "apply:schema": "node src/scripts/apply_chat_sessions_schema.js", "install:database": "node src/scripts/install-database.js", "validate:database": "node src/scripts/validate-database.js", "db:migrate": "node src/scripts/migrate.js", "db:migrate:queue": "node src/scripts/run-migration.js 024_add_document_queue_fields", "db:rollback:queue": "node src/scripts/rollback-migration.js 024_add_document_queue_fields", "db:verify:queue": "node src/scripts/verify-migration.js queue", "setup:database": "node src/scripts/setup-database.js", "worker:start": "node src/workers/documentWorker.js", "clear:chromadb": "node scripts/clear_chromadb_collections.js --force", "clear:chromadb:safe": "node scripts/clear_chromadb_collections.js", "test": "jest"}, "dependencies": {"@heroicons/react": "^2.2.0", "@langchain/community": "^0.3.42", "@langchain/openai": "^0.5.8", "axios": "^1.8.4", "axios-cookiejar-support": "^5.0.5", "bcrypt": "^5.1.1", "better-sqlite3": "^9.4.3", "bullmq": "^5.0.0", "chromadb": "^2.3.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "eventsource": "^2.0.2", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "form-data": "^4.0.0", "http-proxy-middleware": "^2.0.6", "fs-extra": "^11.3.0", "ini": "^4.1.3", "ioredis": "^5.3.2", "langchain": "^0.3.27", "mammoth": "^1.9.0", "multer": "^1.4.5-lts.2", "node-fetch": "^2.7.0", "node-ssh": "^13.1.0", "node-windows": "^1.0.0-beta.8", "ollama": "^0.5.15", "pdf-parse": "^1.1.1", "pg": "^8.14.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "redis": "^4.6.12", "remark-gfm": "^4.0.1", "uuid": "^11.1.0", "winston": "^3.12.0", "yargs": "^17.7.2"}, "devDependencies": {"@types/body-parser": "^1.19.6", "concurrently": "^8.2.2", "jest": "^29.7.0", "nodemon": "^3.1.9"}}